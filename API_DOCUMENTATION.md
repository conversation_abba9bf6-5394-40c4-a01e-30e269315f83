# SpringCloud 聊天微服务 API 文档

## 📋 概述

本文档详细描述了 SpringCloud Alibaba 聊天微服务系统的 API 接口，包括 REST API 和 WebSocket 协议。系统采用 OAuth2 + JWT 认证方式，提供完整的聊天功能接口。

## 🔐 认证方式

### OAuth2 认证流程
系统采用 OAuth2 密码模式进行用户认证：

```http
POST /oauth2/token
Content-Type: application/x-www-form-urlencoded
Authorization: Basic Y2hhdC1jbGllbnQ6Y2hhdC1zZWNyZXQ=

grant_type=password&username=admin&password=123456
```

### JWT Token 使用
获取 access_token 后，在请求头中携带：
```http
Authorization: Bearer {access_token}
```

## 🌐 REST API 接口

### 1. 用户认证接口

#### 1.1 用户注册
```http
POST /api/auth/register
Content-Type: application/json

{
    "username": "testuser",
    "password": "123456",
    "confirmPassword": "123456",
    "nickname": "测试用户",
    "email": "<EMAIL>"
}
```

**响应示例：**
```json
{
    "code": 200,
    "message": "注册成功",
    "data": {
        "id": 1,
        "username": "testuser",
        "nickname": "测试用户",
        "email": "<EMAIL>"
    }
}
```

#### 1.2 用户登录
```http
POST /api/auth/login
Content-Type: application/json

{
    "username": "admin",
    "password": "123456"
}
```

**响应示例：**
```json
{
    "code": 200,
    "message": "登录成功",
    "data": {
        "access_token": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...",
        "token_type": "Bearer",
        "expires_in": 3600,
        "user_info": {
            "id": 1,
            "username": "admin",
            "nickname": "管理员"
        }
    }
}
```

### 2. 聊天室管理接口

#### 2.1 创建聊天室
```http
POST /api/chat/rooms
Content-Type: application/json
Authorization: Bearer {access_token}

{
    "roomName": "技术交流群",
    "description": "技术讨论专用群",
    "roomType": "public",
    "maxUsers": 100,
    "allowAnonymous": false
}
```

#### 2.2 获取聊天室列表
```http
GET /api/chat/rooms?page=1&size=10&type=public
Authorization: Bearer {access_token}
```

#### 2.3 加入聊天室
```http
POST /api/chat/rooms/{roomId}/join
Authorization: Bearer {access_token}
```

#### 2.4 离开聊天室
```http
POST /api/chat/rooms/{roomId}/leave
Authorization: Bearer {access_token}
```

### 3. 消息管理接口

#### 3.1 获取聊天历史
```http
GET /api/chat/messages?roomId={roomId}&page=1&size=20
Authorization: Bearer {access_token}
```

#### 3.2 获取私聊历史
```http
GET /api/chat/messages/private?userId={userId}&page=1&size=20
Authorization: Bearer {access_token}
```

### 4. 文件上传接口

#### 4.1 上传文件
```http
POST /api/files/upload
Content-Type: multipart/form-data
Authorization: Bearer {access_token}

file: [文件数据]
```

**响应示例：**
```json
{
    "code": 200,
    "message": "上传成功",
    "data": {
        "fileId": "file_123456",
        "fileName": "image.jpg",
        "fileSize": 1024000,
        "fileType": "image/jpeg",
        "downloadUrl": "/api/files/download/file_123456"
    }
}
```

### 5. 离线消息接口

#### 5.1 获取离线消息
```http
GET /api/chat/offline-messages
Authorization: Bearer {access_token}
```

#### 5.2 标记消息已读
```http
POST /api/chat/offline-messages/read
Content-Type: application/json
Authorization: Bearer {access_token}

{
    "messageIds": ["msg_001", "msg_002"]
}
```

## 🔌 WebSocket 协议

### 连接建立
```javascript
const ws = new WebSocket('ws://localhost:9090/ws?token={access_token}');
```

### 消息格式

#### 基础消息结构
```json
{
    "type": "message_type",
    "data": {
        // 消息数据
    },
    "timestamp": 1640995200000
}
```

### 消息类型

#### 1. 认证消息
**客户端发送：**
```json
{
    "type": "auth",
    "data": {
        "token": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9..."
    }
}
```

**服务端响应：**
```json
{
    "type": "auth_result",
    "data": {
        "success": true,
        "userId": 1,
        "username": "admin",
        "message": "认证成功"
    }
}
```

#### 2. 聊天消息
**发送消息：**
```json
{
    "type": "chat_message",
    "data": {
        "messageId": "msg_123456",
        "messageType": "text",
        "content": "Hello, World!",
        "roomId": "public_general",
        "referenceMessageId": "msg_123455"
    }
}
```

**接收消息：**
```json
{
    "type": "chat_message",
    "data": {
        "messageId": "msg_123456",
        "senderId": 1,
        "senderName": "admin",
        "messageType": "text",
        "content": "Hello, World!",
        "roomId": "public_general",
        "sendTime": 1640995200000,
        "referenceMessageId": "msg_123455",
        "referenceContent": "之前的消息内容"
    }
}
```

#### 3. 私聊消息
```json
{
    "type": "private_message",
    "data": {
        "messageId": "msg_123456",
        "messageType": "text",
        "content": "私聊消息",
        "receiverId": 2
    }
}
```

#### 4. 用户状态消息
```json
{
    "type": "user_status",
    "data": {
        "userId": 1,
        "username": "admin",
        "status": "online",
        "roomId": "public_general"
    }
}
```

#### 5. 在线用户列表
```json
{
    "type": "user_list",
    "data": {
        "roomId": "public_general",
        "users": [
            {
                "userId": 1,
                "username": "admin",
                "nickname": "管理员",
                "status": "online"
            }
        ],
        "totalCount": 1
    }
}
```

#### 6. 文件消息
```json
{
    "type": "chat_message",
    "data": {
        "messageId": "msg_123456",
        "messageType": "file",
        "content": "文件名.pdf",
        "roomId": "public_general",
        "extraData": {
            "fileId": "file_123456",
            "fileName": "文件名.pdf",
            "fileSize": 1024000,
            "fileType": "application/pdf",
            "downloadUrl": "/api/files/download/file_123456"
        }
    }
}
```

#### 7. 心跳消息
**客户端发送：**
```json
{
    "type": "ping"
}
```

**服务端响应：**
```json
{
    "type": "pong",
    "data": {
        "timestamp": 1640995200000
    }
}
```

## 📊 错误码说明

| 错误码 | 说明 | 处理建议 |
|--------|------|----------|
| 200 | 成功 | - |
| 400 | 请求参数错误 | 检查请求参数格式 |
| 401 | 未认证 | 重新登录获取token |
| 403 | 权限不足 | 检查用户权限 |
| 404 | 资源不存在 | 检查请求路径 |
| 500 | 服务器内部错误 | 联系管理员 |
| 1001 | 用户不存在 | 检查用户信息 |
| 1002 | 密码错误 | 重新输入密码 |
| 1003 | 用户已存在 | 使用其他用户名 |
| 2001 | 房间不存在 | 检查房间ID |
| 2002 | 房间已满 | 选择其他房间 |
| 2003 | 无权限访问房间 | 检查房间权限 |
| 3001 | 消息格式错误 | 检查消息格式 |
| 3002 | 消息内容过长 | 减少消息长度 |
| 4001 | 文件类型不支持 | 使用支持的文件类型 |
| 4002 | 文件大小超限 | 减小文件大小 |

## 🔧 SDK 示例

### JavaScript SDK 示例
```javascript
class ChatClient {
    constructor(wsUrl, token) {
        this.wsUrl = wsUrl;
        this.token = token;
        this.ws = null;
    }
    
    connect() {
        this.ws = new WebSocket(`${this.wsUrl}?token=${this.token}`);
        
        this.ws.onopen = () => {
            console.log('WebSocket连接已建立');
            this.authenticate();
        };
        
        this.ws.onmessage = (event) => {
            const message = JSON.parse(event.data);
            this.handleMessage(message);
        };
        
        this.ws.onclose = () => {
            console.log('WebSocket连接已关闭');
        };
    }
    
    authenticate() {
        this.send({
            type: 'auth',
            data: { token: this.token }
        });
    }
    
    sendMessage(content, roomId, messageType = 'text') {
        this.send({
            type: 'chat_message',
            data: {
                messageId: this.generateMessageId(),
                messageType: messageType,
                content: content,
                roomId: roomId
            }
        });
    }
    
    send(message) {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            this.ws.send(JSON.stringify(message));
        }
    }
    
    handleMessage(message) {
        switch (message.type) {
            case 'auth_result':
                console.log('认证结果:', message.data);
                break;
            case 'chat_message':
                console.log('收到消息:', message.data);
                break;
            case 'user_list':
                console.log('用户列表:', message.data);
                break;
        }
    }
    
    generateMessageId() {
        return 'msg_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
}

// 使用示例
const client = new ChatClient('ws://localhost:9090/ws', 'your_access_token');
client.connect();
```

## 📝 注意事项

1. **Token 有效期**: access_token 默认有效期为 1 小时，过期后需要重新获取
2. **连接限制**: 单个用户最多支持 5 个并发 WebSocket 连接
3. **消息限制**: 单条消息最大长度为 1000 字符
4. **文件限制**: 单个文件最大 10MB，支持常见文件类型
5. **频率限制**: 单用户每秒最多发送 10 条消息
6. **房间限制**: 单个房间最多 500 个用户

## 🔄 版本更新

### v1.2.2 (2025-01-27)
- 新增引用回复功能 API
- 完善 OAuth2 认证流程
- 优化文件上传接口
- 增强错误处理机制

### v1.2.1 (2025-01-26)
- 修复用户列表同步问题
- 优化消息类型处理
- 增强连接稳定性
