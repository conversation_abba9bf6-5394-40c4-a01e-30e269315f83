package com.xiang.chat.core;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class R<T> implements Serializable {
    private boolean success;
    private String message;
    private T data;

    public static <T> R<T> success(T data) {
        return new R<>(true, "success", data);
    }

    public static <T> R<T> success() {
        return new R<>(true, "success", null);
    }

    public static <T> R<T> error(String message) {
        return new R<>(false, message, null);
    }
}
