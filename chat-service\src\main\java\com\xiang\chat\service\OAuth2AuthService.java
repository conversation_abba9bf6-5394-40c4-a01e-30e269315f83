package com.xiang.chat.service;

import com.xiang.chat.dto.OAuth2TokenResponse;
import com.xiang.chat.feign.OAuth2FeignService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Base64;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class OAuth2AuthService {

    @Value("${oauth2.auth-server.client-id}")
    private String clientId;

    @Value("${oauth2.auth-server.client-secret}")
    private String clientSecret;

    private final OAuth2FeignService oAuth2FeignService;

    /**
     * 通过用户名密码获取OAuth2 Token
     */
    public OAuth2TokenResponse authenticate(String username, String password) {
        try {
            log.debug("Starting OAuth2 authentication for user: {}", username);

            // 构建Basic认证头
            String credentials = clientId + ":" + clientSecret;
            String encodedCredentials = Base64.getEncoder().encodeToString(credentials.getBytes());
            String authorization = "Basic " + encodedCredentials;

            log.debug("OAuth2 client credentials: {}, encoded: {}", credentials, encodedCredentials);

            // 使用Feign调用OAuth2 token端点
            Map<String, Object> responseBody = oAuth2FeignService.getOAuth2Token(
                    authorization,
                    "password",
                    username,
                    password,
                    "read write");

            log.debug("OAuth2 response received: {}", responseBody);

            // 检查OAuth2响应中是否有错误
            if (responseBody.containsKey("error")) {
                String error = (String) responseBody.get("error");
                String errorDescription = (String) responseBody.get("error_description");

                log.warn("OAuth2 authentication failed: error={}, description={}", error, errorDescription);

                return OAuth2TokenResponse.builder()
                        .success(false)
                        .error(error)
                        .errorDescription(errorDescription != null ? errorDescription : getErrorDescription(error))
                        .build();
            }

            // 成功获取token
            return OAuth2TokenResponse.builder()
                    .accessToken((String) responseBody.get("access_token"))
                    .tokenType((String) responseBody.get("token_type"))
                    .expiresIn((Integer) responseBody.get("expires_in"))
                    .refreshToken((String) responseBody.get("refresh_token"))
                    .scope((String) responseBody.get("scope"))
                    .success(true)
                    .build();

        } catch (feign.FeignException.Unauthorized e) {
            log.warn("OAuth2 authentication failed - Unauthorized: username={}", username);
            return OAuth2TokenResponse.builder()
                    .success(false)
                    .error("invalid_credentials")
                    .errorDescription("用户名或密码错误")
                    .build();
        } catch (feign.FeignException.BadRequest e) {
            log.warn("OAuth2 authentication failed - Bad Request: username={}", username);
            return OAuth2TokenResponse.builder()
                    .success(false)
                    .error("invalid_request")
                    .errorDescription("请求参数错误")
                    .build();
        } catch (feign.FeignException e) {
            log.error("OAuth2 authentication failed - Feign error: status={}, message={}", e.status(), e.getMessage());
            return OAuth2TokenResponse.builder()
                    .success(false)
                    .error("server_error")
                    .errorDescription("认证服务器错误，请稍后重试")
                    .build();
        } catch (Exception e) {
            log.error("OAuth2 authentication error for user: {}", username, e);
            return OAuth2TokenResponse.builder()
                    .success(false)
                    .error("authentication_error")
                    .errorDescription("认证过程中发生错误: " + e.getMessage())
                    .build();
        }
    }

    /**
     * 根据错误代码获取错误描述
     */
    private String getErrorDescription(String error) {
        return switch (error) {
            case "invalid_credentials", "invalid_grant" -> "用户名或密码错误";
            case "invalid_request" -> "请求参数错误";
            case "server_unavailable" -> "认证服务器不可用，请稍后重试";
            case "unsupported_grant_type" -> "不支持的授权类型";
            case "invalid_scope" -> "无效的权限范围";
            default -> "认证失败: " + error;
        };
    }
}