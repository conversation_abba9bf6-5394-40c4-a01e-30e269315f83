package com.xiang.chat.controller;

import com.xiang.chat.core.R;
import com.xiang.chat.dto.RegisterRequest;
import com.xiang.chat.dto.RegisterResponse;
import com.xiang.chat.feign.AuthFeignService;
import com.xiang.chat.dto.OAuth2TokenResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

@Slf4j
@Controller
@RequiredArgsConstructor
public class LoginController {

    private final AuthFeignService authFeignService;

    @GetMapping("/")
    public String index() {
        return "redirect:/login";
    }

    @GetMapping("/login")
    public String login() {
        return "login";
    }

    @GetMapping("/chat")
    public String chat(Model model) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

        if (authentication != null && authentication.isAuthenticated() &&
            !authentication.getName().equals("anonymousUser")) {
            String username = authentication.getName();
            model.addAttribute("username", username);

            // 获取OAuth2 token信息并传递到页面
            if (authentication.getDetails() instanceof OAuth2TokenResponse tokenResponse) {
                String accessToken = tokenResponse.getAccessToken();
                String tokenType = tokenResponse.getTokenType();
                Integer expiresIn = tokenResponse.getExpiresIn();

                model.addAttribute("accessToken", accessToken);
                model.addAttribute("tokenType", tokenType);
                model.addAttribute("expiresIn", expiresIn);

                log.info("User {} accessed chat page with JWT token: tokenType={}, tokenLength={}, expiresIn={}",
                    username, tokenType, accessToken != null ? accessToken.length() : 0, expiresIn);
            } else {
                log.warn("User {} accessed chat page without JWT token, details type: {}",
                    username, authentication.getDetails() != null ? authentication.getDetails().getClass().getSimpleName() : "null");
                // 如果没有token，可能需要重新认证
                model.addAttribute("accessToken", "");
                model.addAttribute("tokenType", "");
            }
        } else {
            model.addAttribute("username", "Guest");
            model.addAttribute("accessToken", "");
            model.addAttribute("tokenType", "");
            log.warn("Unauthenticated user trying to access chat page");
        }

        return "chat";
    }

    @GetMapping("/register")
    public String register() {
        return "register";
    }

    @PostMapping("/api/register")
    @ResponseBody
    public R<RegisterResponse> registerUser(@Valid @RequestBody RegisterRequest registerRequest) {
        try {
            log.info("User registration request: username={}, email={}",
                    registerRequest.getUsername(), registerRequest.getEmail());

            R<RegisterResponse> result = authFeignService.register(registerRequest);

            if (result.isSuccess()) {
                log.info("User registered successfully: username={}", registerRequest.getUsername());
            } else {
                log.warn("User registration failed: {}", result.getMessage());
            }

            return result;

        } catch (Exception e) {
            log.error("User registration error: {}", e.getMessage(), e);
            return R.error("注册失败，请稍后重试");
        }
    }

    @GetMapping("/api/register/check-username")
    @ResponseBody
    public R<Boolean> checkUsername(@RequestParam String username) {
        try {
            return authFeignService.checkUsername(username);
        } catch (Exception e) {
            log.error("Check username error: {}", e.getMessage(), e);
            return R.error("检查用户名失败");
        }
    }

    @GetMapping("/api/register/check-email")
    @ResponseBody
    public R<Boolean> checkEmail(@RequestParam String email) {
        try {
            return authFeignService.checkEmail(email);
        } catch (Exception e) {
            log.error("Check email error: {}", e.getMessage(), e);
            return R.error("检查邮箱失败");
        }
    }
}