<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f5f5;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            font-size: 24px;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .user-name {
            font-weight: 500;
        }

        .logout-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            transition: background 0.3s;
        }

        .logout-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }


        .chat-container {
            flex: 1;
            display: flex;
            max-height: calc(100vh - 70px);
        }

        .sidebar {
            width: 300px;
            background: white;
            border-right: 1px solid #e0e0e0;
            display: flex;
            flex-direction: column;
        }

        .sidebar-tabs {
            display: flex;
            background: #fafafa;
            border-bottom: 1px solid #e0e0e0;
        }

        .sidebar-tab {
            flex: 1;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.3s;
        }

        .sidebar-tab.active {
            background: white;
            border-bottom-color: #667eea;
            color: #667eea;
        }

        .sidebar-tab:hover {
            background: #f0f0f0;
        }

        .sidebar-content {
            flex: 1;
            overflow-y: auto;
        }

        .tab-panel {
            display: none;
            height: 100%;
        }

        .tab-panel.active {
            display: flex;
            flex-direction: column;
        }

        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid #e0e0e0;
            background: #fafafa;
        }

        .sidebar-header h3 {
            color: #333;
            margin-bottom: 10px;
        }

        .online-users,
        .room-list {
            flex: 1;
            overflow-y: auto;
        }

        .user-item {
            padding: 12px 20px;
            border-bottom: 1px solid #f0f0f0;
            cursor: pointer;
            transition: background 0.2s;
            position: relative;
        }

        .user-item:hover {
            background: #f5f5f5;
        }

        .user-item.active {
            background: #e3f2fd;
            border-left: 3px solid #2196f3;
        }

        .user-item.self {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .user-item.self::after {
            content: "(我)";
            position: absolute;
            right: 20px;
            font-size: 12px;
            color: #999;
        }

        .room-item {
            padding: 12px 20px;
            border-bottom: 1px solid #f0f0f0;
            cursor: pointer;
            transition: background 0.2s;
            position: relative;
        }

        .room-item:hover {
            background: #f5f5f5;
        }

        .room-item.active {
            background: #e3f2fd;
            border-left: 3px solid #2196f3;
        }

        .room-item.current {
            background: #e8f5e8;
            border-left: 3px solid #4caf50;
        }

        .room-controls {
            padding: 15px 20px;
            border-bottom: 1px solid #e0e0e0;
            background: #fafafa;
        }

        .room-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 10px;
            font-size: 14px;
        }

        .room-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            margin-right: 5px;
            margin-bottom: 5px;
        }

        .room-btn:hover {
            background: #5a67d8;
        }

        .room-btn.danger {
            background: #e53e3e;
        }

        .room-btn.danger:hover {
            background: #c53030;
        }

        .chat-placeholder {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 16px;
        }

        .main-chat {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: white;
        }

        .chat-header {
            padding: 20px;
            border-bottom: 1px solid #e0e0e0;
            background: #fafafa;
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f9f9f9;
        }

        .message {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-start;
            gap: 10px;
        }

        .message.own {
            flex-direction: row-reverse;
        }

        .message-content {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            background: white;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .message.own .message-content {
            background: #667eea;
            color: white;
        }

        .message-info {
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
        }

        .message.own .message-info {
            color: rgba(255, 255, 255, 0.8);
        }

        .chat-input {
            padding: 20px;
            border-top: 1px solid #e0e0e0;
            background: white;
        }

        .input-container {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .message-input {
            flex: 1;
            padding: 12px 16px;
            border: 1px solid #ddd;
            border-radius: 25px;
            font-size: 14px;
            outline: none;
        }

        .message-input:focus {
            border-color: #667eea;
        }

        .send-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 500;
            transition: transform 0.2s;
        }

        .send-btn:hover {
            transform: translateY(-1px);
        }

        .status {
            padding: 10px 20px;
            background: #e8f5e8;
            color: #2e7d32;
            text-align: center;
            font-size: 14px;
        }

        .status.error {
            background: #ffebee;
            color: #c62828;
        }

        /* 消息类型样式 */
        .message.sent {
            display: flex;
            justify-content: flex-end;
        }

        .message.sent .message-content {
            background: #667eea;
            color: white;
        }

        .message.received {
            display: flex;
            justify-content: flex-start;
        }

        .message.received .message-content {
            background: white;
            color: #333;
        }

        .message.sent .message-info {
            color: rgba(255, 255, 255, 0.8);
        }

        /* 引用回复相关样式 */
        .reply-preview {
            display: none;
            background: #e9ecef;
            border-left: 3px solid #6f42c1;
            padding: 10px;
            margin-bottom: 10px;
            border-radius: 5px;
            position: relative;
        }

        .reply-preview .close-btn {
            position: absolute;
            top: 5px;
            right: 10px;
            cursor: pointer;
            font-size: 18px;
            color: #6c757d;
        }

        .reply-preview .sender {
            font-weight: bold;
            color: #6f42c1;
        }

        .reply-preview .content {
            margin-top: 5px;
            color: #6c757d;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .message.reply {
            border-left: 3px solid #6f42c1;
            background: #f8f9ff;
        }

        .reply-reference {
            background: #e9ecef;
            border-left: 3px solid #6f42c1;
            padding: 8px;
            margin-bottom: 8px;
            border-radius: 3px;
            cursor: pointer;
        }

        .reply-reference:hover {
            background: #dee2e6;
        }

        .reply-reference .sender {
            font-size: 12px;
            font-weight: bold;
            color: #6f42c1;
        }

        .reply-reference .content {
            font-size: 12px;
            color: #6c757d;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .message-actions {
            margin-top: 5px;
            text-align: right;
        }

        .reply-btn {
            background: #6f42c1;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 3px;
            font-size: 12px;
            cursor: pointer;
        }

        .reply-btn:hover {
            background: #5a2d91;
        }

        /* 离线消息相关样式 */
        .offline-message-indicator {
            background: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 8px 12px;
            margin: 10px 0;
            border-radius: 4px;
            font-size: 12px;
            color: #856404;
            text-align: center;
        }

        .message.offline {
            opacity: 0.9;
        }

        .message.offline .message-content {
            border-left: 3px solid #ffc107;
        }

        .offline-count {
            background: #dc3545;
            color: white;
            border-radius: 50%;
            padding: 2px 6px;
            font-size: 10px;
            margin-left: 5px;
            display: inline-block;
            min-width: 16px;
            text-align: center;
        }

        .user-item.has-offline {
            background: #fff3cd;
            border-left: 3px solid #ffc107;
        }

        .user-item.has-offline:hover {
            background: #ffeaa7;
        }

        /* 离线消息相关样式 */
        .offline-message-indicator {
            background: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 8px 12px;
            margin: 10px 0;
            border-radius: 4px;
            font-size: 12px;
            color: #856404;
            text-align: center;
        }

        .message.offline {
            opacity: 0.9;
        }

        .message.offline .message-content {
            border-left: 3px solid #ffc107;
        }

        .offline-count {
            background: #dc3545;
            color: white;
            border-radius: 50%;
            padding: 2px 6px;
            font-size: 10px;
            margin-left: 5px;
            display: inline-block;
            min-width: 16px;
            text-align: center;
        }

        .user-item.has-offline {
            background: #fff3cd;
            border-left: 3px solid #ffc107;
        }

        .user-item.has-offline:hover {
            background: #ffeaa7;
        }
    </style>
</head>

<body>
    <div class="header">
        <h1>💬 聊天系统</h1>
        <div class="user-info">
            <span class="user-name">欢迎，<span th:text="${username ?: 'Guest'}">用户</span></span>
            <span th:if="${tokenType}" class="token-info" style="font-size: 12px; opacity: 0.8;">
                (OAuth2 认证)
            </span>

            <form th:action="@{/logout}" method="post" style="display: inline;">
                <button type="submit" class="logout-btn">退出登录</button>
            </form>
        </div>
    </div>

    <div class="chat-container">
        <div class="sidebar">
            <div class="sidebar-tabs">
                <div class="sidebar-tab active" onclick="switchTab('users')">👥 用户</div>
                <div class="sidebar-tab" onclick="switchTab('rooms')">🏠 群组</div>
                <div class="sidebar-tab" onclick="switchTab('offline')">📬 离线</div>
            </div>

            <div class="sidebar-content">
                <!-- 用户标签页 -->
                <div class="tab-panel active" id="usersTab">
                    <div class="sidebar-header">
                        <h3>在线用户</h3>
                        <div id="onlineCount">0 人在线</div>
                        <div id="offlineMessageHint"
                            style="font-size: 12px; color: #ffc107; margin-top: 5px; display: none;">
                            📬 您有离线消息，请查看
                        </div>
                        <div style="font-size: 12px; color: #666; margin-top: 5px;">点击用户开始私聊</div>
                    </div>
                    <div class="online-users" id="onlineUsers">
                        <!-- 在线用户列表 -->
                    </div>
                </div>

                <!-- 群组标签页 -->
                <div class="tab-panel" id="roomsTab">
                    <div class="room-controls">
                        <input type="text" id="roomIdInput" class="room-input" placeholder="输入群组ID" value="room_001">
                        <button class="room-btn" onclick="joinRoom()">加入群组</button>
                        <button class="room-btn danger" onclick="leaveRoom()">离开群组</button>
                        <button class="room-btn" onclick="loadPublicRooms()">刷新列表</button>
                    </div>
                    <div class="sidebar-header">
                        <h3>可用群组</h3>
                        <div id="roomCount">加载中...</div>
                        <div style="font-size: 12px; color: #666; margin-top: 5px;">点击群组开始群聊</div>
                    </div>
                    <div class="room-list" id="roomList">
                        <!-- 群组列表 -->
                    </div>
                </div>

                <!-- 离线消息标签页 -->
                <div class="tab-panel" id="offlineTab">
                    <div class="room-controls">
                        <button class="room-btn" onclick="refreshOfflineMessages()">刷新离线消息</button>
                        <button class="room-btn" onclick="getOfflineMessageCountViaHttp()">获取消息数量</button>
                        <button class="room-btn" onclick="getOfflineMessageStatsViaHttp()">查看统计</button>
                        <button class="room-btn danger" onclick="clearOfflineMessagesViaHttp()">清除所有</button>
                    </div>
                    <div class="sidebar-header">
                        <h3>离线消息管理</h3>
                        <div id="offlineMessageSummary">
                            <div>总数量: <span id="offlineCountDisplay">0</span></div>
                            <div style="font-size: 12px; color: #666; margin-top: 5px;">管理您的离线消息</div>
                        </div>
                    </div>
                    <div class="room-list" id="offlineMessageList">
                        <!-- 离线消息列表 -->
                    </div>
                </div>
            </div>
        </div>

        <div class="main-chat">
            <div class="chat-header">
                <h3 id="chatTitle">选择用户或群组开始聊天</h3>
                <div id="chatSubtitle" style="font-size: 14px; color: #666; margin-top: 5px;"></div>
            </div>

            <div id="status" class="status" style="display: none;"></div>

            <div class="chat-messages" id="messages">
                <div class="chat-placeholder" id="chatPlaceholder">
                    <div>
                        <div style="font-size: 48px; margin-bottom: 20px;">💬</div>
                        <div>选择一个用户开始私聊，或选择一个群组开始群聊</div>
                    </div>
                </div>
            </div>

            <div class="chat-input" id="chatInput" style="display: none;">
                <!-- 回复预览区域 -->
                <div class="reply-preview" id="replyPreview">
                    <span class="close-btn" onclick="cancelReply()">×</span>
                    <div>回复 <span class="sender" id="replySender"></span>:</div>
                    <div class="content" id="replyContent"></div>
                    <input type="hidden" id="referenceMessageId">
                    <input type="hidden" id="referenceSenderId">
                </div>

                <div class="input-container">
                    <input type="text" id="messageInput" class="message-input" placeholder="输入消息..." maxlength="500">
                    <button id="sendBtn" class="send-btn">发送</button>
                </div>
            </div>
        </div>
        <div class="form-hidden" style="display: none;">
            <input type="hidden" name="username" th:value="${username}">
            <input type="hidden" name="accessToken" th:value="${accessToken}">
            <input type="hidden" name="tokenType" th:value="${tokenType}">
        </div>
    </div>

    <script>
        var formDom = document.querySelector('.form-hidden');
        const username = formDom.querySelector('input[name="username"]').value;
        const accessToken = formDom.querySelector('input[name="accessToken"]').value;
        window.accessToken = accessToken;
        const tokenType = formDom.querySelector('input[name="tokenType"]').value;
        let websocket = null;
        let isConnected = false;
        let currentChatUser = null; // 当前私聊的用户
        let currentRoomId = null; // 当前群组ID
        let chatType = 'private'; // 'private' 或 'group'
        let chatHistory = new Map(); // 存储每个用户的聊天历史
        let roomHistory = new Map(); // 存储每个群组的聊天历史
        let offlineMessages = new Map(); // 存储离线消息，按发送者ID分组
        let offlineMessageCount = 0; // 离线消息总数

        // 调试信息
        console.log('页面变量初始化:', {
            username: username,
            tokenType: tokenType,
            accessTokenLength: accessToken ? accessToken.length : 0,
            accessTokenPreview: accessToken ? accessToken.substring(0, 20) + '...' : 'empty'
        });

        // 初始化WebSocket连接
        function initWebSocket() {
            try {
                websocket = new WebSocket('ws://localhost:9090/ws');

                websocket.onopen = function () {
                    console.log('WebSocket连接已建立');
                    isConnected = true;
                    showStatus('已连接到服务器', false);

                    // 使用JWT token进行认证
                    const authMessage = {
                        type: 'auth',
                        data: {
                            token: accessToken,
                            tokenType: tokenType,
                            username: username
                        },
                        timestamp: Date.now()
                    };

                    console.log('发送JWT认证消息:', {
                        type: 'auth',
                        username: username,
                        tokenType: tokenType,
                        tokenLength: accessToken ? accessToken.length : 0
                    });

                    websocket.send(JSON.stringify(authMessage));
                };

                websocket.onmessage = function (event) {
                    const message = JSON.parse(event.data);
                    handleMessage(message);
                };

                websocket.onclose = function () {
                    console.log('WebSocket连接已关闭');
                    isConnected = false;
                    showStatus('连接已断开，正在重连...', true);

                    // 3秒后重连
                    setTimeout(() => {
                        initWebSocket();
                        // 重连后重新获取离线消息
                        setTimeout(() => {
                            if (currentUserId && isConnected) {
                                requestOfflineMessages();
                            }
                        }, 1000);
                    }, 3000);
                };

                websocket.onerror = function (error) {
                    console.error('WebSocket错误:', error);
                    showStatus('连接错误', true);
                };
            } catch (error) {
                console.error('WebSocket初始化失败:', error);
                showStatus('连接失败', true);
            }
        }

        // 处理接收到的消息
        function handleMessage(message) {
            console.log('收到消息:', message);

            switch (message.type) {
                case 'auth_success':
                    console.log('认证成功');
                    // 保存当前用户ID
                    if (message.data && message.data.userId) {
                        currentUserId = message.data.userId;
                        console.log('当前用户ID:', currentUserId);
                    }
                    showStatus('认证成功', false);
                    setTimeout(() => hideStatus(), 2000);
                    // 认证成功后，请求当前在线用户列表
                    requestOnlineUsers();
                    // 主动请求离线消息（作为备用）
                    setTimeout(() => {
                        console.log('认证成功后主动请求离线消息...');
                        requestOfflineMessages();
                    }, 1000);
                    // 延迟更新用户列表，确保离线消息已处理
                    setTimeout(() => {
                        if (document.getElementById('onlineUsers').userData) {
                            updateUserList(document.getElementById('onlineUsers').userData);
                        }
                    }, 2000);
                    break;
                case 'auth_failure':
                    console.log('认证失败:', message.data.message);
                    showStatus('认证失败: ' + message.data.message, true);
                    break;
                case 'private_message':
                    handlePrivateMessage(message.data);
                    break;
                case 'group_message':
                    handleGroupMessage(message.data);
                    break;
                case 'room_joined':
                case 'join_room_success':
                    handleRoomJoined(message.data);
                    break;
                case 'room_left':
                case 'leave_room_success':
                    handleRoomLeft(message.data);
                    break;
                case 'user_joined':
                    handleUserJoined(message.data);
                    break;
                case 'user_left':
                    handleUserLeft(message.data);
                    break;
                case 'user_list':
                case 'online_users':
                    // 处理用户列表更新（支持两种消息类型）
                    updateUserList(message.data.users);
                    break;
                case 'system_message':
                    displaySystemMessage(message.data.message);
                    break;
                case 'offline_messages':
                    console.log('收到离线消息响应:', message.data);
                    handleOfflineMessages(message.data);
                    break;
                case 'error':
                    console.error('服务器错误:', message.data);
                    showStatus('服务器错误: ' + message.data.message, true);
                    break;
                default:
                    console.log('未知消息类型:', message.type);
            }
        }

        // 发送消息（支持私聊和群聊）
        function sendMessage() {
            const input = document.getElementById('messageInput');
            const messageText = input.value.trim();

            if (!messageText || !isConnected) {
                return;
            }

            // 检查是否有聊天对象
            if (chatType === 'private' && !currentChatUser) {
                showStatus('请先选择一个用户开始私聊', true);
                return;
            }
            if (chatType === 'group' && !currentRoomId) {
                showStatus('请先加入一个群组开始群聊', true);
                return;
            }

            // 生成消息ID
            const messageId = 'msg_' + Date.now() + '_' + (currentUserId || 'unknown') + '_' + Math.random().toString(36).substr(2, 9);

            const message = {
                type: 'chat',
                data: {
                    messageId: messageId,
                    content: messageText,
                    messageType: 'text',
                    sendTime: Date.now()
                },
                timestamp: Date.now()
            };

            // 根据聊天类型设置接收者
            if (chatType === 'private') {
                message.data.receiverId = currentChatUser.id;
            } else if (chatType === 'group') {
                message.data.roomId = currentRoomId;
            }

            // 检查是否有引用消息
            const referenceMessageId = document.getElementById('referenceMessageId').value;
            const referenceSenderId = document.getElementById('referenceSenderId').value;

            if (referenceMessageId && referenceSenderId) {
                message.data.referenceMessageId = referenceMessageId;
                message.data.referenceSenderId = parseInt(referenceSenderId);
                message.data.referenceContent = document.getElementById('replyContent').textContent;
            }

            // 构建引用数据用于显示
            let referenceData = null;
            if (referenceMessageId && referenceSenderId) {
                referenceData = {
                    messageId: referenceMessageId,
                    senderId: referenceSenderId,
                    content: document.getElementById('replyContent').textContent
                };
            }

            // 记录发送的消息
            sentMessages.set(messageId, {
                content: messageText,
                timestamp: Date.now(),
                status: 'sending'
            });

            // 立即显示发送的消息
            let displayText = messageText;
            if (chatType === 'private') {
                displayText = `[私聊给 ${currentChatUser.nickname || currentChatUser.username}] ${messageText}`;
            } else if (chatType === 'group') {
                displayText = `[群聊-${currentRoomId}] ${messageText}`;
            }

            addMessage('sent', displayText, Date.now(), messageId, null, referenceData, null, false);

            // 发送消息
            websocket.send(JSON.stringify(message));

            console.log('消息已发送:', message);
            input.value = '';

            // 清空回复引用
            cancelReply();
        }

        // 添加消息到界面 - 支持引用回复
        function addMessage(type, content, timestamp, messageId, senderName, referenceData, senderId, isOffline = false) {
            console.log('addMessage被调用:', {
                type,
                content,
                timestamp,
                messageId,
                senderName,
                referenceData,
                senderId,
                isOffline
            });

            const messagesDiv = document.getElementById('messages');
            if (!messagesDiv) {
                console.error('找不到messages元素');
                return;
            }

            const messageDiv = document.createElement('div');
            let className = `message ${type}`;
            if (referenceData) {
                className = `message reply`;
            }
            if (isOffline) {
                className += ' offline';
            }
            messageDiv.className = className;

            // 如果有messageId，设置为元素的data属性
            if (messageId) {
                messageDiv.setAttribute('data-message-id', messageId);
            }

            const time = timestamp ? new Date(timestamp).toLocaleTimeString() : new Date().toLocaleTimeString();

            // 根据消息类型显示不同的发送者信息
            let senderDisplay = '';
            if (type === 'sent') {
                senderDisplay = '我';
            } else if (type === 'received') {
                senderDisplay = senderName || '未知用户';
            }

            let messageHTML = '';

            // 添加引用消息预览
            if (referenceData) {
                console.log('添加引用消息预览:', referenceData);
                messageHTML += `
                    <div class="reply-reference" onclick="highlightReferencedMessage('${referenceData.messageId}')">
                        <div class="sender">回复 ${referenceData.senderId}:</div>
                        <div class="content">${escapeHtml(referenceData.content)}</div>
                    </div>
                `;
            } else {
                console.log('没有引用数据，不添加引用预览');
            }

            messageHTML += `
                <div class="message-content">
                    <div class="message-info">${senderDisplay} • ${time}${isOffline ? ' • 📬 离线消息' : ''}</div>
                    <div>${escapeHtml(content)}</div>
                </div>
            `;

            // 只为接收到的消息添加回复按钮
            if (type === 'received' && messageId && senderId) {
                messageHTML += `
                    <div class="message-actions">
                        <button class="reply-btn" onclick="replyToMessage('${messageId}', '${senderId}', '${escapeHtml(content)}')">回复</button>
                    </div>
                `;
            }

            messageDiv.innerHTML = messageHTML;
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;

            console.log('消息已添加到DOM，当前消息数量:', messagesDiv.children.length);
        }

        // 显示消息 - 保留原有接口兼容性
        function displayMessage(data) {
            console.log('displayMessage被调用，数据:', data);

            const type = data.isOwn ? 'sent' : 'received';
            const senderName = data.isOwn ? null : data.sender;

            // 检查是否是离线消息
            const messageId = data.messageId || null;
            const senderId = data.isOwn ? null : data.sender;

            addMessage(type, data.content, data.timestamp, messageId, senderName, null, senderId, data.isOffline || false);
        }

        // 回复消息
        function replyToMessage(messageId, senderId, content) {
            document.getElementById('replyPreview').style.display = 'block';
            document.getElementById('replySender').textContent = senderId;
            document.getElementById('replyContent').textContent = content;
            document.getElementById('referenceMessageId').value = messageId;
            document.getElementById('referenceSenderId').value = senderId;

            document.getElementById('messageInput').focus();
            console.log(`设置回复消息引用: messageId=${messageId}, senderId=${senderId}`);
        }

        // 取消回复
        function cancelReply() {
            document.getElementById('replyPreview').style.display = 'none';
            document.getElementById('referenceMessageId').value = '';
            document.getElementById('referenceSenderId').value = '';
        }

        // 高亮引用的消息
        function highlightReferencedMessage(messageId) {
            const messageElement = document.querySelector(`[data-message-id="${messageId}"]`);
            if (messageElement) {
                messageElement.style.backgroundColor = '#fff3cd';
                setTimeout(() => {
                    messageElement.style.backgroundColor = '';
                }, 2000);

                // 滚动到引用的消息
                messageElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        }

        // 处理私聊消息 - 参考index_test.html的实现
        function handlePrivateMessage(data) {
            console.log('收到私聊消息数据:', data);

            const messageId = data.messageId;
            const senderId = data.senderId;
            const receiverId = data.receiverId;
            const content = data.content;
            const timestamp = data.sendTime || Date.now();

            // 检查是否是自己发送的消息
            const isOwnMessage = senderId == getCurrentUserId();

            console.log('私聊消息详情:', {
                messageId,
                senderId,
                receiverId,
                content,
                timestamp,
                isOwnMessage,
                currentUserId: getCurrentUserId()
            });

            // 从在线用户列表中查找发送者信息
            let senderName = senderId.toString();
            const userListDiv = document.getElementById('onlineUsers');
            if (userListDiv.userData) {
                const senderUser = userListDiv.userData.find(user =>
                    user.id == senderId
                );
                if (senderUser) {
                    senderName = senderUser.nickname || senderUser.username || senderId.toString();
                }
            }

            // 构建引用消息数据（如果有）
            let referenceData = null;
            if (data.referenceMessageId) {
                referenceData = {
                    messageId: data.referenceMessageId,
                    senderId: data.referenceSenderId,
                    content: data.referenceContent || ''
                };
                console.log('构建引用数据:', referenceData);
            } else {
                console.log('没有引用数据');
            }

            // 显示消息
            if (isOwnMessage) {
                // 如果是自己的消息，检查是否已经显示过（避免重复）
                if (sentMessages.has(messageId)) {
                    console.log(`私聊消息已送达: messageId=${messageId}`);
                    return;
                }
                // 自己发送的消息
                const displayText = `[私聊给 ${receiverId}] ${content}`;
                addMessage('sent', displayText, timestamp, messageId, null, referenceData, null, false);
            } else {
                // 别人发送的消息，传递发送者名称和ID
                const displayText = `[私聊] ${senderName}: ${content}`;
                addMessage('received', displayText, timestamp, messageId, senderName, referenceData, senderId, false);
            }

            // 添加到聊天历史
            const messageData = {
                sender: senderName,
                content: content,
                timestamp: timestamp,
                isOwn: isOwnMessage
            };

            // 根据是否是自己的消息来决定使用哪个用户ID作为key
            const chatUserId = isOwnMessage ? receiverId : senderId;
            addToChatHistory(String(chatUserId), messageData);
        }

        // 处理群聊消息
        function handleGroupMessage(data) {
            console.log('收到群聊消息数据:', data);

            const messageId = data.messageId;
            const senderId = data.senderId;
            const roomId = data.roomId;
            const content = data.content;
            const timestamp = data.sendTime || Date.now();

            // 检查是否是自己发送的消息
            const isOwnMessage = senderId == getCurrentUserId();

            console.log('群聊消息详情:', {
                messageId,
                senderId,
                roomId,
                content,
                timestamp,
                isOwnMessage,
                currentUserId: getCurrentUserId()
            });

            // 从在线用户列表中查找发送者信息
            let senderName = senderId.toString();
            const userListDiv = document.getElementById('onlineUsers');
            if (userListDiv.userData) {
                const senderUser = userListDiv.userData.find(user =>
                    user.id == senderId
                );
                if (senderUser) {
                    senderName = senderUser.nickname || senderUser.username || senderId.toString();
                }
            }

            // 构建引用消息数据（如果有）
            let referenceData = null;
            if (data.referenceMessageId) {
                referenceData = {
                    messageId: data.referenceMessageId,
                    senderId: data.referenceSenderId,
                    content: data.referenceContent || ''
                };
                console.log('构建群聊引用数据:', referenceData);
            }

            // 显示消息
            if (isOwnMessage) {
                // 如果是自己的消息，检查是否已经显示过（避免重复）
                if (sentMessages.has(messageId)) {
                    console.log(`群聊消息已送达: messageId=${messageId}`);
                    return;
                }
                // 自己发送的消息
                const displayText = `[群聊-${roomId}] ${content}`;
                addMessage('sent', displayText, timestamp, messageId, null, referenceData, null, false);
            } else {
                // 别人发送的消息，传递发送者名称和ID
                const displayText = `[群聊-${roomId}] ${senderName}: ${content}`;
                addMessage('received', displayText, timestamp, messageId, senderName, referenceData, senderId, false);
            }

            // 添加到群组聊天历史
            const messageData = {
                sender: senderName,
                content: content,
                timestamp: timestamp,
                isOwn: isOwnMessage,
                roomId: roomId
            };

            addToRoomHistory(roomId, messageData);
        }

        // 全局变量存储当前用户ID
        let currentUserId = null;

        // 跟踪已发送的消息，避免重复显示
        const sentMessages = new Map();

        // 获取当前用户ID的辅助函数
        function getCurrentUserId() {
            if (currentUserId) {
                return currentUserId.toString();
            }

            // 从在线用户列表中查找当前用户的ID
            const userListDiv = document.getElementById('onlineUsers');
            if (userListDiv.userData) {
                const currentUser = userListDiv.userData.find(user =>
                    user.username === username || user.nickname === username
                );
                if (currentUser) {
                    currentUserId = currentUser.id;
                    return currentUser.id.toString();
                }
            }
            return null;
        }

        // 显示系统消息
        function displaySystemMessage(message) {
            const messagesDiv = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message system';
            messageDiv.innerHTML = `
                <div class="message-content" style="background: #f0f0f0; color: #666; text-align: center;">
                    ${escapeHtml(message)}
                </div>
            `;
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        // 更新用户列表
        function updateUserList(users) {
            const userListDiv = document.getElementById('onlineUsers');
            const countDiv = document.getElementById('onlineCount');

            if (!users || !Array.isArray(users)) {
                console.warn('用户列表数据无效:', users);
                countDiv.textContent = '0 人在线';
                userListDiv.innerHTML = '<div class="user-item">暂无在线用户</div>';
                return;
            }

            countDiv.textContent = users.length + ' 人在线';

            // 保存用户数据供其他函数使用
            userListDiv.userData = users;

            userListDiv.innerHTML = '';
            users.forEach(user => {
                const userDiv = document.createElement('div');

                // 处理用户信息显示
                let displayName = '';
                let userObj = null;

                if (typeof user === 'object' && user !== null) {
                    userObj = user;
                    displayName = user.nickname || user.username || '未知用户';
                    userDiv.title = `用户名: ${user.username || '未知'}\n昵称: ${user.nickname || '未设置'}\nID: ${user.id || '未知'}`;
                } else if (typeof user === 'string' || typeof user === 'number') {
                    userObj = { id: user, username: user.toString(), nickname: user.toString() };
                    displayName = user.toString();
                } else {
                    displayName = '未知用户';
                }

                // 检查是否是当前用户
                const isCurrentUser = userObj && (userObj.username === username || userObj.nickname === username);

                if (isCurrentUser) {
                    userDiv.className = 'user-item self';
                } else {
                    userDiv.className = 'user-item';
                    // 添加点击事件开始私聊
                    userDiv.addEventListener('click', () => startPrivateChat(userObj));
                }

                // 检查是否是当前聊天用户
                if (currentChatUser && userObj && userObj.id === currentChatUser.id) {
                    userDiv.classList.add('active');
                }

                userDiv.textContent = displayName;
                userListDiv.appendChild(userDiv);
            });

            console.log('用户列表已更新:', users.length, '个用户');
        }

        // 开始私聊
        function startPrivateChat(user) {
            if (!user || !user.id) {
                console.warn('无效的用户信息:', user);
                return;
            }

            currentChatUser = user;
            currentRoomId = null; // 清空群组ID
            chatType = 'private';

            // 更新聊天标题
            const chatTitle = document.getElementById('chatTitle');
            const chatSubtitle = document.getElementById('chatSubtitle');
            const displayName = user.nickname || user.username || '未知用户';

            // 检查是否有离线消息
            const userOfflineMessages = offlineMessages.get(String(user.id)) || [];
            const offlineCount = userOfflineMessages.length;

            chatTitle.textContent = `与 ${displayName} 的私聊${offlineCount > 0 ? ` (${offlineCount}条离线消息)` : ''}`;
            chatSubtitle.textContent = `用户ID: ${user.id}`;

            // 显示聊天输入框
            document.getElementById('chatInput').style.display = 'block';
            document.getElementById('chatPlaceholder').style.display = 'none';

            // 清空消息区域并加载历史消息
            const messagesDiv = document.getElementById('messages');
            messagesDiv.innerHTML = '';

            // 加载该用户的聊天历史和离线消息
            const userIdKey = String(user.id);
            const history = chatHistory.get(userIdKey) || [];
            const userOfflineMessages = offlineMessages.get(userIdKey) || [];

            console.log('加载聊天历史，用户ID:', userIdKey, '历史消息数量:', history.length, '离线消息数量:', userOfflineMessages.length);

            // 首先显示历史消息
            history.forEach(msg => {
                console.log('显示历史消息:', msg);
                const messageType = msg.isOwn ? 'sent' : 'received';
                const senderName = msg.isOwn ? null : msg.sender;
                const senderId = msg.isOwn ? null : user.id;

                addMessage(messageType, msg.content, msg.timestamp, msg.messageId, senderName, null, senderId, msg.isOffline || false);
            });

            // 然后处理离线消息：将其添加到聊天历史并显示
            if (userOfflineMessages.length > 0) {
                console.log('处理离线消息，数量:', userOfflineMessages.length);

                userOfflineMessages.forEach(offlineMsg => {
                    console.log('显示离线消息:', offlineMsg);

                    // 创建消息数据并添加到聊天历史
                    const messageData = {
                        sender: offlineMsg.senderName,
                        content: offlineMsg.content,
                        timestamp: offlineMsg.sendTime,
                        isOwn: false,
                        isOffline: true,
                        messageId: offlineMsg.messageId
                    };

                    addToChatHistory(userIdKey, messageData);

                    // 在界面上显示离线消息
                    addMessage('received', offlineMsg.content, offlineMsg.sendTime, offlineMsg.messageId,
                              offlineMsg.senderName, null, offlineMsg.senderId, true);
                });
            }

            // 如果有离线消息，显示离线消息指示器
            if (offlineCount > 0) {
                displayOfflineMessageIndicator(offlineCount);
            }

            // 更新用户列表中的选中状态
            updateUserList(document.getElementById('onlineUsers').userData || []);

            // 清空群组列表的选中状态
            updateRoomListSelection(null);

            // 更新输入框占位符
            document.getElementById('messageInput').placeholder = '输入私聊消息...';

            // 聚焦到输入框
            document.getElementById('messageInput').focus();

            // 标记该用户的离线消息为已读
            markOfflineMessagesAsRead(user.id);

            console.log('开始与用户私聊:', displayName, user.id);
        }

        // 添加到聊天历史
        function addToChatHistory(userId, messageData) {
            // 确保userId是字符串类型，保持一致性
            const userIdKey = String(userId);

            if (!chatHistory.has(userIdKey)) {
                chatHistory.set(userIdKey, []);
            }
            chatHistory.get(userIdKey).push(messageData);

            // 限制历史消息数量
            const history = chatHistory.get(userIdKey);
            if (history.length > 100) {
                history.splice(0, history.length - 100);
            }

            console.log('消息已添加到聊天历史，用户ID:', userIdKey, '历史消息数量:', history.length);
        }

        // 添加到群组聊天历史
        function addToRoomHistory(roomId, messageData) {
            const roomIdKey = String(roomId);

            if (!roomHistory.has(roomIdKey)) {
                roomHistory.set(roomIdKey, []);
            }
            roomHistory.get(roomIdKey).push(messageData);

            // 限制历史消息数量
            const history = roomHistory.get(roomIdKey);
            if (history.length > 100) {
                history.splice(0, history.length - 100);
            }

            console.log('消息已添加到群组历史，群组ID:', roomIdKey, '历史消息数量:', history.length);
        }

        // 切换侧边栏标签页
        function switchTab(tabName) {
            // 更新标签页状态
            const tabs = document.querySelectorAll('.sidebar-tab');
            const panels = document.querySelectorAll('.tab-panel');

            tabs.forEach(tab => tab.classList.remove('active'));
            panels.forEach(panel => panel.classList.remove('active'));

            // 激活选中的标签页
            if (tabName === 'users') {
                tabs[0].classList.add('active');
                document.getElementById('usersTab').classList.add('active');
            } else if (tabName === 'rooms') {
                tabs[1].classList.add('active');
                document.getElementById('roomsTab').classList.add('active');
                // 加载群组列表
                loadPublicRooms();
            } else if (tabName === 'offline') {
                tabs[2].classList.add('active');
                document.getElementById('offlineTab').classList.add('active');
                // 加载离线消息管理界面
                loadOfflineMessageManagement();
            }

            console.log('切换到标签页:', tabName);
        }

        // 加入群组
        function joinRoom() {
            if (!isConnected) {
                showStatus('请先连接WebSocket', true);
                return;
            }

            const roomId = document.getElementById('roomIdInput').value.trim();
            if (!roomId) {
                showStatus('请输入群组ID', true);
                return;
            }

            const message = {
                type: 'join_room',
                data: { roomId: roomId },
                timestamp: Date.now()
            };

            websocket.send(JSON.stringify(message));
            console.log('已发送加入群组请求:', roomId);
            showStatus('正在加入群组: ' + roomId, false);
        }

        // 离开群组
        function leaveRoom() {
            if (!isConnected) {
                showStatus('请先连接WebSocket', true);
                return;
            }

            if (!currentRoomId) {
                showStatus('当前未在任何群组中', true);
                return;
            }

            const message = {
                type: 'leave_room',
                data: { roomId: currentRoomId },
                timestamp: Date.now()
            };

            websocket.send(JSON.stringify(message));
            console.log('已发送离开群组请求:', currentRoomId);
            showStatus('正在离开群组: ' + currentRoomId, false);
        }

        // 处理群组加入成功
        function handleRoomJoined(data) {
            const roomId = data.roomId;
            currentRoomId = roomId;
            chatType = 'group';

            // 更新聊天标题
            const chatTitle = document.getElementById('chatTitle');
            const chatSubtitle = document.getElementById('chatSubtitle');

            chatTitle.textContent = `群聊 - ${roomId}`;
            chatSubtitle.textContent = `群组ID: ${roomId}`;

            // 显示聊天输入框
            document.getElementById('chatInput').style.display = 'block';
            document.getElementById('chatPlaceholder').style.display = 'none';

            // 清空消息区域并加载历史消息
            const messagesDiv = document.getElementById('messages');
            messagesDiv.innerHTML = '';

            // 加载该群组的聊天历史
            const history = roomHistory.get(roomId) || [];
            console.log('加载群组聊天历史，群组ID:', roomId, '历史消息数量:', history.length);
            history.forEach(msg => {
                const displayText = `[群聊-${roomId}] ${msg.isOwn ? '我' : msg.sender}: ${msg.content}`;
                addMessage(msg.isOwn ? 'sent' : 'received', displayText, msg.timestamp, null, null, null, null, msg.isOffline || false);
            });

            // 更新群组列表中的选中状态
            updateRoomListSelection(roomId);

            // 更新输入框占位符
            document.getElementById('messageInput').placeholder = '输入群聊消息...';

            // 聚焦到输入框
            document.getElementById('messageInput').focus();

            showStatus(`成功加入群组: ${roomId}`, false);
            setTimeout(() => hideStatus(), 2000);

            console.log('成功加入群组:', roomId);
        }

        // 处理群组离开
        function handleRoomLeft(data) {
            const roomId = data.roomId;

            if (currentRoomId === roomId) {
                currentRoomId = null;
                chatType = 'private';

                // 更新聊天标题
                const chatTitle = document.getElementById('chatTitle');
                const chatSubtitle = document.getElementById('chatSubtitle');

                chatTitle.textContent = '选择用户或群组开始聊天';
                chatSubtitle.textContent = '';

                // 隐藏聊天输入框
                document.getElementById('chatInput').style.display = 'none';
                document.getElementById('chatPlaceholder').style.display = 'flex';

                // 清空消息区域
                document.getElementById('messages').innerHTML = '';

                // 重置输入框占位符
                document.getElementById('messageInput').placeholder = '输入消息...';
            }

            // 更新群组列表中的选中状态
            updateRoomListSelection(null);

            showStatus(`已离开群组: ${roomId}`, false);
            setTimeout(() => hideStatus(), 2000);

            console.log('已离开群组:', roomId);
        }

        // 处理用户加入群组
        function handleUserJoined(data) {
            const userId = data.userId;
            const roomId = data.roomId;
            displaySystemMessage(`用户 ${userId} 加入了群组 ${roomId}`);
        }

        // 处理用户离开群组
        function handleUserLeft(data) {
            const userId = data.userId;
            const roomId = data.roomId;
            displaySystemMessage(`用户 ${userId} 离开了群组 ${roomId}`);
        }

        // 开始群聊
        function startGroupChat(roomId) {
            if (!roomId) {
                console.warn('无效的群组ID:', roomId);
                return;
            }

            // 设置群组ID到输入框并加入群组
            document.getElementById('roomIdInput').value = roomId;
            joinRoom();
        }

        // 更新群组列表选中状态
        function updateRoomListSelection(selectedRoomId) {
            const roomItems = document.querySelectorAll('.room-item');
            roomItems.forEach(item => {
                item.classList.remove('active', 'current');
                if (selectedRoomId && item.textContent.includes(selectedRoomId)) {
                    item.classList.add('current');
                }
            });
        }

        // 加载公开群组列表
        function loadPublicRooms() {
            const roomList = document.getElementById('roomList');
            const roomCount = document.getElementById('roomCount');

            // 显示默认群组列表
            const defaultRooms = [
                { roomId: 'public_general', roomName: '大厅', description: '公共聊天大厅' },
                { roomId: 'public_tech', roomName: '技术交流', description: '技术讨论群组' },
                { roomId: 'public_random', roomName: '随便聊聊', description: '随意聊天群组' },
                { roomId: 'room_001', roomName: '测试房间', description: '测试专用群组' },
                { roomId: 'room_002', roomName: '开发讨论', description: '开发相关讨论' }
            ];

            roomList.innerHTML = '';
            defaultRooms.forEach(room => {
                const roomDiv = document.createElement('div');
                roomDiv.className = 'room-item';
                roomDiv.title = room.description;

                // 检查是否是当前群组
                if (currentRoomId === room.roomId) {
                    roomDiv.classList.add('current');
                }

                roomDiv.addEventListener('click', () => startGroupChat(room.roomId));
                roomDiv.innerHTML = `
                    <div style="font-weight: bold;">${room.roomName}</div>
                    <div style="font-size: 12px; color: #666;">${room.roomId}</div>
                `;
                roomList.appendChild(roomDiv);
            });

            roomCount.textContent = `${defaultRooms.length} 个可用群组`;
            console.log('群组列表已更新:', defaultRooms.length, '个群组');
        }

        // 加载离线消息管理界面
        function loadOfflineMessageManagement() {
            updateOfflineMessageDisplay();
            loadOfflineMessageList();
        }

        // 更新页面标题
        function updatePageTitle() {
            const baseTitle = '聊天系统';
            if (offlineMessageCount > 0) {
                document.title = `(${offlineMessageCount}) ${baseTitle}`;
            } else {
                document.title = baseTitle;
            }
        }

        // 加载离线消息列表
        function loadOfflineMessageList() {
            const listDiv = document.getElementById('offlineMessageList');
            if (!listDiv) return;

            listDiv.innerHTML = '';

            if (offlineMessages.size === 0) {
                listDiv.innerHTML = '<div class="room-item" style="text-align: center; color: #666;">暂无离线消息</div>';
                return;
            }

            // 按发送者显示离线消息
            offlineMessages.forEach((messages, senderId) => {
                const senderDiv = document.createElement('div');
                senderDiv.className = 'room-item';
                senderDiv.style.cursor = 'pointer';

                // 查找发送者信息
                let senderName = senderId;
                const userListDiv = document.getElementById('onlineUsers');
                if (userListDiv.userData) {
                    const senderUser = userListDiv.userData.find(user => String(user.id) === senderId);
                    if (senderUser) {
                        senderName = senderUser.nickname || senderUser.username || senderId;
                    }
                }

                senderDiv.innerHTML = `
                    <div style="font-weight: bold;">${senderName}</div>
                    <div style="font-size: 12px; color: #666;">${messages.length} 条离线消息</div>
                    <div style="font-size: 11px; color: #999; margin-top: 2px;">
                        最新: ${messages.length > 0 ? formatTime(messages[messages.length - 1].sendTime || messages[messages.length - 1].timestamp) : '无'}
                    </div>
                `;

                // 点击查看该用户的离线消息
                senderDiv.addEventListener('click', () => {
                    viewOfflineMessagesFromSender(senderId, senderName);
                });

                listDiv.appendChild(senderDiv);
            });
        }

        // 查看特定发送者的离线消息
        function viewOfflineMessagesFromSender(senderId, senderName) {
            const messages = offlineMessages.get(String(senderId));
            if (!messages || messages.length === 0) {
                showStatus('该用户没有离线消息', true);
                return;
            }

            // 切换到用户标签页并开始私聊
            switchTab('users');

            // 查找用户对象
            const userListDiv = document.getElementById('onlineUsers');
            if (userListDiv.userData) {
                const user = userListDiv.userData.find(u => String(u.id) === String(senderId));
                if (user) {
                    startPrivateChat(user);
                } else {
                    // 如果用户不在线，创建一个临时用户对象
                    const tempUser = {
                        id: parseInt(senderId),
                        username: senderName,
                        nickname: senderName
                    };
                    startPrivateChat(tempUser);
                }
            }
        }

        // 刷新离线消息
        function refreshOfflineMessages() {
            showStatus('正在刷新离线消息...', false);

            // 优先使用WebSocket请求
            if (websocket && websocket.readyState === WebSocket.OPEN) {
                requestOfflineMessages();
            } else {
                // WebSocket不可用时使用HTTP请求作为备用
                getOfflineMessagesViaHttp()
                    .then(() => {
                        loadOfflineMessageManagement();
                        showStatus('离线消息已刷新', false);
                        setTimeout(() => hideStatus(), 2000);
                    })
                    .catch(error => {
                        console.error('刷新离线消息失败:', error);
                        showStatus('刷新失败: ' + error.message, true);
                        setTimeout(() => hideStatus(), 3000);
                    });
            }
        }

        // 请求在线用户列表
        function requestOnlineUsers() {
            if (!websocket || websocket.readyState !== WebSocket.OPEN) {
                console.warn('WebSocket连接未就绪，无法请求用户列表');
                return;
            }

            const requestMessage = {
                type: 'get_online_users',
                data: {
                    roomId: 'default' // 默认房间
                },
                timestamp: Date.now()
            };

            console.log('请求在线用户列表');
            websocket.send(JSON.stringify(requestMessage));
        }

        // 请求离线消息
        function requestOfflineMessages() {
            if (!websocket || websocket.readyState !== WebSocket.OPEN) {
                console.warn('WebSocket连接未就绪，无法请求离线消息');
                return;
            }

            const requestMessage = {
                type: 'get_offline_messages',
                data: {
                    userId: currentUserId
                },
                timestamp: Date.now()
            };

            console.log('请求离线消息');
            websocket.send(JSON.stringify(requestMessage));
        }

        // 处理离线消息
        function handleOfflineMessages(data) {
            console.log('收到离线消息数据:', data);

            if (!data.messages || !Array.isArray(data.messages)) {
                console.log('没有离线消息');
                return;
            }

            const messages = data.messages;
            offlineMessageCount = messages.length;

            if (offlineMessageCount > 0) {
                console.log(`收到 ${offlineMessageCount} 条离线消息`);
                showStatus(`您有 ${offlineMessageCount} 条离线消息`, false);
                setTimeout(() => hideStatus(), 3000);

                // 显示离线消息提示
                const offlineHint = document.getElementById('offlineMessageHint');
                if (offlineHint) {
                    offlineHint.style.display = 'block';
                    offlineHint.textContent = `📬 您有 ${offlineMessageCount} 条离线消息`;
                }

                // 按发送者分组离线消息并立即显示
                messages.forEach(msg => {
                    console.log('处理离线消息:', msg);

                    // 从消息数据中提取发送者信息
                    let senderId = null;
                    let senderName = null;
                    let content = '';
                    let timestamp = msg.timestamp || Date.now();
                    let messageId = msg.messageId;
                    let receiverId = null;

                    if (msg.data) {
                        senderId = msg.data.senderId || msg.senderId;
                        receiverId = msg.data.receiverId;
                        content = msg.data.content || '';
                        timestamp = msg.data.sendTime || timestamp;
                        messageId = msg.data.messageId || messageId;

                        // 尝试从用户列表获取发送者名称
                        const userListDiv = document.getElementById('onlineUsers');
                        if (userListDiv.userData && senderId) {
                            const senderUser = userListDiv.userData.find(user => user.id == senderId);
                            if (senderUser) {
                                senderName = senderUser.nickname || senderUser.username;
                            }
                        }
                        senderName = senderName || `用户${senderId}`;
                    } else {
                        // 兼容旧格式
                        senderId = msg.senderId;
                        receiverId = msg.receiverId;
                        content = msg.content || '';
                        timestamp = msg.sendTime || timestamp;
                        senderName = msg.senderName || `用户${senderId}`;
                    }

                    if (senderId) {
                        const senderIdKey = String(senderId);

                        // 存储离线消息
                        if (!offlineMessages.has(senderIdKey)) {
                            offlineMessages.set(senderIdKey, []);
                        }
                        offlineMessages.get(senderIdKey).push({
                            messageId: messageId,
                            senderId: senderId,
                            senderName: senderName,
                            content: content,
                            sendTime: timestamp,
                            type: msg.type
                        });

                        // 只有当前正在与该用户聊天时，才添加到聊天历史并立即显示
                        if (chatType === 'private' && currentChatUser && String(currentChatUser.id) === senderIdKey) {
                            console.log('当前正在与发送者聊天，添加离线消息到聊天历史并立即显示');

                            const messageData = {
                                sender: senderName,
                                content: content,
                                timestamp: timestamp,
                                isOwn: false,
                                isOffline: true,
                                messageId: messageId
                            };

                            addToChatHistory(senderIdKey, messageData);
                            addMessage('received', content, timestamp, messageId, senderName, null, senderId, true);
                        } else {
                            console.log(`离线消息暂存，等待用户点击私聊时显示: senderId=${senderId}, content=${content}`);
                        }
                    }
                });

                // 更新用户列表，显示有离线消息的用户
                updateUserListWithOfflineIndicators();

                // 显示离线消息提示
                displayOfflineMessageIndicator(offlineMessageCount);

                // 更新离线消息管理界面
                updateOfflineMessageDisplay();
                loadOfflineMessageList();

                // 更新页面标题
                updatePageTitle();
            }
        }

        // 显示离线消息指示器
        function displayOfflineMessageIndicator(count) {
            const messagesDiv = document.getElementById('messages');

            // 检查是否已经有离线消息指示器
            const existingIndicator = messagesDiv.querySelector('.offline-message-indicator');
            if (existingIndicator) {
                existingIndicator.remove();
            }

            const indicatorDiv = document.createElement('div');
            indicatorDiv.className = 'offline-message-indicator';
            indicatorDiv.innerHTML = `
                📬 您有 ${count} 条离线消息，点击对应用户查看详情
            `;

            // 插入到消息区域的开头
            if (messagesDiv.firstChild && messagesDiv.firstChild.className !== 'chat-placeholder') {
                messagesDiv.insertBefore(indicatorDiv, messagesDiv.firstChild);
            } else {
                messagesDiv.appendChild(indicatorDiv);
            }
        }

        // 更新用户列表，显示离线消息指示器
        function updateUserListWithOfflineIndicators() {
            const userListDiv = document.getElementById('onlineUsers');
            if (!userListDiv.userData) return;

            // 重新渲染用户列表
            updateUserList(userListDiv.userData);

            // 为有离线消息的用户添加指示器
            offlineMessages.forEach((messages, senderId) => {
                const userItems = userListDiv.querySelectorAll('.user-item');
                userItems.forEach(item => {
                    // 检查用户项是否对应有离线消息的发送者
                    const userData = userListDiv.userData.find(user =>
                        String(user.id) === senderId
                    );

                    if (userData && item.textContent.includes(userData.nickname || userData.username)) {
                        item.classList.add('has-offline');

                        // 添加离线消息数量标识
                        const countSpan = document.createElement('span');
                        countSpan.className = 'offline-count';
                        countSpan.textContent = messages.length;
                        item.appendChild(countSpan);
                    }
                });
            });
        }

        // 标记离线消息为已读
        function markOfflineMessagesAsRead(senderId) {
            if (!websocket || websocket.readyState !== WebSocket.OPEN) {
                console.warn('WebSocket连接未就绪，无法标记消息为已读');
                return;
            }

            const senderIdKey = String(senderId);
            const userOfflineMessages = offlineMessages.get(senderIdKey);

            if (!userOfflineMessages || userOfflineMessages.length === 0) {
                return;
            }

            const messageIds = userOfflineMessages.map(msg => msg.messageId);

            const markReadMessage = {
                type: 'mark_messages_read',
                data: {
                    messageIds: messageIds,
                    senderId: senderId
                },
                timestamp: Date.now()
            };

            websocket.send(JSON.stringify(markReadMessage));
            console.log('标记离线消息为已读:', messageIds);

            // 清除本地离线消息记录
            offlineMessages.delete(senderIdKey);
            offlineMessageCount -= userOfflineMessages.length;

            // 更新用户列表，移除离线消息指示器
            updateUserListWithOfflineIndicators();

            // 如果没有更多离线消息，移除指示器
            if (offlineMessageCount <= 0) {
                const indicator = document.querySelector('.offline-message-indicator');
                if (indicator) {
                    indicator.remove();
                }
                // 隐藏离线消息提示
                const offlineHint = document.getElementById('offlineMessageHint');
                if (offlineHint) {
                    offlineHint.style.display = 'none';
                }
            } else {
                // 更新指示器数量
                displayOfflineMessageIndicator(offlineMessageCount);
                // 更新离线消息提示
                const offlineHint = document.getElementById('offlineMessageHint');
                if (offlineHint) {
                    offlineHint.textContent = `📬 您有 ${offlineMessageCount} 条离线消息`;
                }
            }

            // 更新离线消息管理界面
            updateOfflineMessageDisplay();
            loadOfflineMessageList();

            // 更新页面标题
            updatePageTitle();
        }

        // 显示状态
        function showStatus(message, isError) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = 'status' + (isError ? ' error' : '');
            statusDiv.style.display = 'block';
        }

        // 隐藏状态
        function hideStatus() {
            document.getElementById('status').style.display = 'none';
        }

        // 格式化时间
        function formatTime(timestamp) {
            // Handle different timestamp formats
            let date;
            if (typeof timestamp === 'string') {
                // Try to parse string timestamps
                date = new Date(timestamp);
                // If invalid, try parsing as milliseconds
                if (isNaN(date.getTime())) {
                    // Try to parse as numeric string
                    const numericTimestamp = parseFloat(timestamp);
                    if (!isNaN(numericTimestamp)) {
                        date = new Date(numericTimestamp);
                    } else {
                        // If still invalid, return error message
                        return 'Invalid Date';
                    }
                }
            } else if (typeof timestamp === 'number') {
                // Handle numeric timestamps
                // Check if it's in seconds (10 digits) or milliseconds (13 digits)
                if (timestamp.toString().length === 10) {
                    // Convert seconds to milliseconds
                    date = new Date(timestamp * 1000);
                } else {
                    // Assume it's already in milliseconds
                    date = new Date(timestamp);
                }
            } else {
                // Handle other cases
                return 'Invalid Date';
            }
            
            // Check if date is valid
            if (isNaN(date.getTime())) {
                return 'Invalid Date';
            }
            
            return date.toLocaleTimeString();
        }

        // HTML转义
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // HTTP请求工具函数 - 自动添加JWT token
        function makeAuthenticatedRequest(url, options = {}) {
            // 确保headers存在
            if (!options.headers) {
                options.headers = {};
            }

            // 添加JWT token到Authorization header
            if (accessToken && tokenType) {
                options.headers['Authorization'] = `${tokenType} ${accessToken}`;
            }

            // 添加Content-Type（如果没有设置）
            if (!options.headers['Content-Type'] && options.method !== 'GET') {
                options.headers['Content-Type'] = 'application/json';
            }

            console.log('发起认证请求:', {
                url: url,
                method: options.method || 'GET',
                hasToken: !!(accessToken && tokenType),
                tokenType: tokenType
            });

            return fetch(url, options)
                .then(response => {
                    // 检查认证状态
                    if (response.status === 401) {
                        console.warn('JWT token已过期或无效，跳转到登录页面');
                        showStatus('登录已过期，请重新登录', true);
                        setTimeout(() => {
                            window.location.href = '/login';
                        }, 2000);
                        throw new Error('认证失败，请重新登录');
                    }

                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return response.json();
                })
                .catch(error => {
                    console.error('HTTP请求失败:', error);
                    throw error;
                });
        }

        // 便捷的GET请求函数
        function apiGet(url) {
            return makeAuthenticatedRequest(url, { method: 'GET' });
        }

        // 便捷的POST请求函数
        function apiPost(url, data) {
            return makeAuthenticatedRequest(url, {
                method: 'POST',
                body: JSON.stringify(data)
            });
        }

        // 便捷的PUT请求函数
        function apiPut(url, data) {
            return makeAuthenticatedRequest(url, {
                method: 'PUT',
                body: JSON.stringify(data)
            });
        }

        // 便捷的DELETE请求函数
        function apiDelete(url) {
            return makeAuthenticatedRequest(url, { method: 'DELETE' });
        }

        // 示例：获取在线用户列表（HTTP方式，作为WebSocket的备用）
        function getOnlineUsersViaHttp() {
            return apiGet('/api/chat/online-users')
                .then(response => {
                    if (response.success && response.data) {
                        console.log('通过HTTP获取在线用户列表成功:', response.data);
                        return response.data;
                    } else {
                        throw new Error(response.message || '获取在线用户列表失败');
                    }
                })
                .catch(error => {
                    console.error('通过HTTP获取在线用户列表失败:', error);
                    throw error;
                });
        }

        // 示例：获取聊天历史（HTTP方式）
        function getChatHistoryViaHttp(otherUserId, page = 1, size = 50) {
            const currentUserId = getCurrentUserId();
            if (!currentUserId) {
                return Promise.reject(new Error('当前用户ID未知'));
            }

            return apiGet(`/api/chat/users/${currentUserId}/private-history?otherUserId=${otherUserId}&page=${page}&size=${size}`)
                .then(response => {
                    if (response.success && response.data) {
                        console.log('通过HTTP获取聊天历史成功:', response.data.length, '条消息');
                        return response.data;
                    } else {
                        throw new Error(response.message || '获取聊天历史失败');
                    }
                })
                .catch(error => {
                    console.error('通过HTTP获取聊天历史失败:', error);
                    throw error;
                });
        }

        // 获取离线消息（HTTP方式，作为WebSocket的备用）
        function getOfflineMessagesViaHttp() {
            const currentUserId = getCurrentUserId();
            if (!currentUserId) {
                return Promise.reject(new Error('当前用户ID未知'));
            }

            return apiGet(`/api/chat/offline/users/${currentUserId}/messages`)
                .then(response => {
                    if (response.success && response.data) {
                        console.log('通过HTTP获取离线消息成功:', response.data.length, '条消息');

                        // 转换HTTP响应格式为WebSocket消息格式
                        const convertedMessages = response.data.map(msg => {
                            // 如果是已经转换过的格式，直接返回
                            if (msg.type && msg.data) {
                                return msg;
                            }

                            // 转换原始消息格式
                            return {
                                type: msg.messageType === 'private' ? 'private_message' : 'group_message',
                                messageId: msg.messageId,
                                senderId: msg.senderId,
                                timestamp: msg.sendTime || msg.timestamp,
                                data: {
                                    messageId: msg.messageId,
                                    senderId: msg.senderId,
                                    receiverId: msg.receiverId,
                                    roomId: msg.roomId,
                                    content: msg.content,
                                    sendTime: msg.sendTime || msg.timestamp,
                                    messageType: msg.messageType || 'text'
                                }
                            };
                        });

                        // 处理离线消息
                        handleOfflineMessages({ messages: convertedMessages });
                        return response.data;
                    } else {
                        throw new Error(response.message || '获取离线消息失败');
                    }
                })
                .catch(error => {
                    console.error('通过HTTP获取离线消息失败:', error);
                    throw error;
                });
        }

        // 获取离线消息数量（HTTP方式）
        function getOfflineMessageCountViaHttp() {
            const currentUserId = getCurrentUserId();
            if (!currentUserId) {
                return Promise.reject(new Error('当前用户ID未知'));
            }

            showStatus('正在获取离线消息数量...', false);

            return apiGet(`/api/chat/offline/users/${currentUserId}/count`)
                .then(response => {
                    if (response.success && typeof response.data === 'number') {
                        console.log('通过HTTP获取离线消息数量成功:', response.data);
                        showStatus(`服务器端离线消息数量: ${response.data}`, false);
                        setTimeout(() => hideStatus(), 3000);
                        return response.data;
                    } else {
                        throw new Error(response.message || '获取离线消息数量失败');
                    }
                })
                .catch(error => {
                    console.error('通过HTTP获取离线消息数量失败:', error);
                    showStatus('获取数量失败: ' + error.message, true);
                    setTimeout(() => hideStatus(), 3000);
                    throw error;
                });
        }

        // 获取离线消息统计（HTTP方式）
        function getOfflineMessageStatsViaHttp() {
            const currentUserId = getCurrentUserId();
            if (!currentUserId) {
                return Promise.reject(new Error('当前用户ID未知'));
            }

            showStatus('正在获取离线消息统计...', false);

            return apiGet(`/api/chat/offline/users/${currentUserId}/stats`)
                .then(response => {
                    if (response.success && response.data) {
                        console.log('通过HTTP获取离线消息统计成功:', response.data);
                        displayOfflineMessageStats(response.data);
                        return response.data;
                    } else {
                        throw new Error(response.message || '获取离线消息统计失败');
                    }
                })
                .catch(error => {
                    console.error('通过HTTP获取离线消息统计失败:', error);
                    showStatus('获取统计失败: ' + error.message, true);
                    setTimeout(() => hideStatus(), 3000);
                    throw error;
                });
        }

        // 显示离线消息统计
        function displayOfflineMessageStats(stats) {
            const statsText = `
离线消息统计:
- 总数量: ${stats.totalCount || 0}
- 未读数量: ${stats.unreadCount || 0}
- 最早消息: ${stats.oldestMessage ? new Date(stats.oldestMessage).toLocaleString() : '无'}
- 最新消息: ${stats.latestMessage ? new Date(stats.latestMessage).toLocaleString() : '无'}
            `.trim();

            // 使用alert显示统计信息（可以后续改为更美观的弹窗）
            alert(statsText);

            showStatus('统计信息已显示', false);
            setTimeout(() => hideStatus(), 2000);
        }

        // 清除离线消息（HTTP方式）
        function clearOfflineMessagesViaHttp() {
            const currentUserId = getCurrentUserId();
            if (!currentUserId) {
                return Promise.reject(new Error('当前用户ID未知'));
            }

            return apiDelete(`/api/chat/offline/users/${currentUserId}/messages`)
                .then(response => {
                    if (response.success) {
                        console.log('通过HTTP清除离线消息成功');
                        // 清除本地离线消息数据
                        offlineMessages.clear();
                        offlineMessageCount = 0;
                        // 更新UI
                        updateUserListWithOfflineIndicators();
                        const indicator = document.querySelector('.offline-message-indicator');
                        if (indicator) {
                            indicator.remove();
                        }
                        showStatus('离线消息已清除', false);
                        setTimeout(() => hideStatus(), 2000);
                        return response.data;
                    } else {
                        throw new Error(response.message || '清除离线消息失败');
                    }
                })
                .catch(error => {
                    console.error('通过HTTP清除离线消息失败:', error);
                    throw error;
                });
        }

        // 标记消息为已读（HTTP方式）
        function markMessagesAsReadViaHttp(messageIds) {
            const currentUserId = getCurrentUserId();
            if (!currentUserId) {
                return Promise.reject(new Error('当前用户ID未知'));
            }

            return apiPost(`/api/chat/users/${currentUserId}/mark-read`, {
                messageIds: messageIds
            })
                .then(response => {
                    if (response.success) {
                        console.log('通过HTTP标记消息为已读成功');
                        return response.data;
                    } else {
                        throw new Error(response.message || '标记消息为已读失败');
                    }
                })
                .catch(error => {
                    console.error('通过HTTP标记消息为已读失败:', error);
                    throw error;
                });
        }


        // 事件监听
        document.getElementById('sendBtn').addEventListener('click', sendMessage);
        document.getElementById('messageInput').addEventListener('keypress', function (e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });

        // 验证JWT token有效性
        function validateTokenOnLoad() {
            if (!accessToken || !tokenType) {
                console.warn('页面加载时未找到JWT token，可能需要重新登录');
                return;
            }

            // 可以选择性地验证token有效性
            console.log('页面加载时JWT token信息:', {
                tokenType: tokenType,
                tokenLength: accessToken.length,
                tokenPreview: accessToken.substring(0, 20) + '...'
            });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function () {
            validateTokenOnLoad();
            initWebSocket();
            // 初始化群组列表
            loadPublicRooms();

            // 延迟获取离线消息（HTTP备用方式）
            setTimeout(() => {
                console.log('检查是否需要通过HTTP获取离线消息:', {
                    currentUserId: currentUserId,
                    offlineMessageCount: offlineMessageCount,
                    isConnected: isConnected,
                    offlineMessagesSize: offlineMessages.size
                });

                // 只有在WebSocket未连接或没有收到离线消息时才使用HTTP方式
                // 同时确保当前用户ID已知
                if ((!isConnected || offlineMessageCount === 0) && currentUserId) {
                    console.log('尝试通过HTTP获取离线消息');
                    getOfflineMessagesViaHttp().catch(error => {
                        console.log('HTTP获取离线消息失败，这是正常的:', error.message);
                    });
                }
            }, 5000); // 增加延迟时间，确保WebSocket认证完成
        });

        // 页面卸载时关闭连接
        window.addEventListener('beforeunload', function () {
            if (websocket) {
                websocket.close();
            }
        });

        // 更新离线消息显示
        function updateOfflineMessageDisplay() {
            const countDisplay = document.getElementById('offlineCountDisplay');
            if (countDisplay) {
                countDisplay.textContent = offlineMessageCount;
            }

            const offlineHint = document.getElementById('offlineMessageHint');
            if (offlineHint) {
                if (offlineMessageCount > 0) {
                    offlineHint.textContent = `📬 您有 ${offlineMessageCount} 条离线消息`;
                    offlineHint.style.display = 'block';
                } else {
                    offlineHint.style.display = 'none';
                }
            }
        }
    </script>
</body>

</html>