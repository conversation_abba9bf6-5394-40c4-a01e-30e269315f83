package com.xiang.chat.service.impl;

import com.xiang.chat.core.R;
import com.xiang.chat.dto.UserInfo;
import com.xiang.chat.feign.AuthFeignService;
import com.xiang.chat.service.UserInfoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 用户信息服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserInfoServiceImpl implements UserInfoService {
    
    private final AuthFeignService authFeignService;
    
    // 用户信息缓存，避免频繁调用auth-service
    private final Map<Long, UserInfo> userInfoCache = new ConcurrentHashMap<>();
    private final Map<Long, Long> cacheTimestamp = new ConcurrentHashMap<>();
    
    // 缓存过期时间：5分钟
    private static final long CACHE_EXPIRE_TIME = 5 * 60 * 1000;
    
    @Override
    public UserInfo getUserInfo(Long userId) {
        if (userId == null) {
            return null;
        }
        
        // 检查缓存
        UserInfo cachedInfo = getCachedUserInfo(userId);
        if (cachedInfo != null) {
            return cachedInfo;
        }
        
        try {
            // 使用Feign调用auth-service获取用户信息
            R<UserInfo> response = authFeignService.getUserInfoById(userId);
            
            if (response.isSuccess() && response.getData() != null) {
                UserInfo userInfo = response.getData();
                // 处理昵称为空的情况
                if (userInfo.getNickname() == null || userInfo.getNickname().trim().isEmpty()) {
                    userInfo.setNickname(userInfo.getUsername());
                }
                // 缓存用户信息
                cacheUserInfo(userId, userInfo);
                return userInfo;
            }
            
            log.warn("获取用户信息失败: userId={}, message={}", userId, response.getMessage());
            
        } catch (Exception e) {
            log.error("调用auth-service获取用户信息异常: userId={}", userId, e);
        }
        
        // 返回默认用户信息
        UserInfo defaultInfo = createDefaultUserInfo(userId);
        cacheUserInfo(userId, defaultInfo);
        return defaultInfo;
    }
    
    @Override
    public Map<Long, UserInfo> getUserInfoBatch(Set<Long> userIds) {
        if (userIds == null || userIds.isEmpty()) {
            return new HashMap<>();
        }
        
        Map<Long, UserInfo> result = new HashMap<>();
        Set<Long> uncachedUserIds = new HashSet<>();
        
        // 先从缓存获取
        for (Long userId : userIds) {
            UserInfo cachedInfo = getCachedUserInfo(userId);
            if (cachedInfo != null) {
                result.put(userId, cachedInfo);
            } else {
                uncachedUserIds.add(userId);
            }
        }
        
        // 批量获取未缓存的用户信息
        if (!uncachedUserIds.isEmpty()) {
            try {
                Map<String, Object> requestBody = new HashMap<>();
                requestBody.put("userIds", uncachedUserIds);
                
                // 使用Feign调用批量获取用户信息
                R<List<UserInfo>> response = authFeignService.getUserInfoBatch(requestBody);
                
                if (response.isSuccess() && response.getData() != null) {
                    List<UserInfo> userInfoList = response.getData();
                    for (UserInfo userInfo : userInfoList) {
                        // 处理昵称为空的情况
                        if (userInfo.getNickname() == null || userInfo.getNickname().trim().isEmpty()) {
                            userInfo.setNickname(userInfo.getUsername());
                        }
                        result.put(userInfo.getId(), userInfo);
                        cacheUserInfo(userInfo.getId(), userInfo);
                    }
                } else {
                    log.warn("批量获取用户信息失败: userIds={}, message={}", uncachedUserIds, response.getMessage());
                }
                
            } catch (Exception e) {
                log.error("批量获取用户信息异常: userIds={}", uncachedUserIds, e);
            }
            
            // 为未获取到的用户创建默认信息
            for (Long userId : uncachedUserIds) {
                if (!result.containsKey(userId)) {
                    UserInfo defaultInfo = createDefaultUserInfo(userId);
                    result.put(userId, defaultInfo);
                    cacheUserInfo(userId, defaultInfo);
                }
            }
        }
        
        return result;
    }
    
    @Override
    public List<UserInfo> getUserInfoList(Set<Long> userIds) {
        Map<Long, UserInfo> userInfoMap = getUserInfoBatch(userIds);
        return userIds.stream()
            .map(userInfoMap::get)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
    }
    
    /**
     * 从缓存获取用户信息
     */
    private UserInfo getCachedUserInfo(Long userId) {
        Long timestamp = cacheTimestamp.get(userId);
        if (timestamp != null && System.currentTimeMillis() - timestamp < CACHE_EXPIRE_TIME) {
            return userInfoCache.get(userId);
        }
        
        // 缓存过期，清理
        userInfoCache.remove(userId);
        cacheTimestamp.remove(userId);
        return null;
    }
    
    /**
     * 缓存用户信息
     */
    private void cacheUserInfo(Long userId, UserInfo userInfo) {
        userInfoCache.put(userId, userInfo);
        cacheTimestamp.put(userId, System.currentTimeMillis());
    }
    

    
    /**
     * 创建默认用户信息
     */
    private UserInfo createDefaultUserInfo(Long userId) {
        UserInfo userInfo = new UserInfo();
        userInfo.setId(userId);
        String defaultUsername = "访客" + userId;
        userInfo.setUsername(defaultUsername);
        userInfo.setNickname(defaultUsername);
        userInfo.setEmail("");
        userInfo.setRole("USER");
        userInfo.setAvatarUrl("");
        return userInfo;
    }
}
