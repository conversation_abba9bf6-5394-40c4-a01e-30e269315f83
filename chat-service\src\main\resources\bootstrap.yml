spring:
  application:
    name: chat-service
  profiles:
    active: dev
  config:
    import: nacos:chat-service-dev.yml
  cloud:
    openfeign:
      circuitbreaker:
        enabled: true
        # 确保使用Resilience4j而不是Hystrix
        group:
          enabled: false
    nacos:
      discovery:
        server-addr: 192.168.5.134:8848
        namespace: public
        group: DEFAULT_GROUP
        username: nacos
        password: nacos
        enabled: true
        register-enabled: true
      config:
        #        server-addr: 192.168.5.134:8848
        server-addr: 192.168.5.134:8848
        namespace: public
        group: DEFAULT_GROUP
        username: nacos
        password: nacos
        file-extension: yml
        enabled: true
        # 配置文件名，默认为应用名
        name: ${spring.application.name}
        # 是否开启监听和自动刷新
        refresh-enabled: false