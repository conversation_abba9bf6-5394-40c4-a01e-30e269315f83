# SpringCloud 聊天微服务数据库设计文档

## 📋 概述

本文档详细描述了 SpringCloud Alibaba 聊天微服务系统的数据库设计，包括表结构、索引、约束关系以及数据字典。系统采用MySQL 8.0+作为主数据库，支持高并发聊天场景的数据存储需求。

## 🗄️ 数据库信息

### 基本信息
- **数据库名称**: `springcloud_chat`
- **字符集**: `utf8mb4`
- **排序规则**: `utf8mb4_unicode_ci`
- **数据库引擎**: InnoDB
- **时区**: Asia/Shanghai
- **MySQL版本**: 8.0+

### 连接配置
```yaml
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **************************************************************************************************************************************************************
    username: root
    password: 123456
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
```

## 📊 数据表设计

### 1. 聊天消息表 (chat_message)

#### 表结构
```sql
CREATE TABLE chat_message (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    message_id VARCHAR(64) NOT NULL UNIQUE COMMENT '消息ID（唯一标识）',
    sender_id BIGINT NOT NULL COMMENT '发送者ID',
    receiver_id BIGINT NULL COMMENT '接收者ID（私聊时使用）',
    room_id VARCHAR(64) NULL COMMENT '房间ID（群聊时使用）',
    message_type VARCHAR(20) NOT NULL DEFAULT 'text' COMMENT '消息类型',
    content TEXT NOT NULL COMMENT '消息内容',
    extra_data JSON NULL COMMENT '消息扩展数据（JSON格式）',
    send_time DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '发送时间',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '消息状态',
    create_time DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    update_time DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
    deleted TINYINT NOT NULL DEFAULT 0 COMMENT '逻辑删除标志'
);
```

#### 字段说明
| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | BIGINT | 主键ID | PRIMARY KEY, AUTO_INCREMENT |
| message_id | VARCHAR(64) | 消息唯一标识 | NOT NULL, UNIQUE |
| sender_id | BIGINT | 发送者用户ID | NOT NULL |
| receiver_id | BIGINT | 接收者用户ID（私聊） | NULL |
| room_id | VARCHAR(64) | 房间ID（群聊） | NULL |
| message_type | VARCHAR(20) | 消息类型 | NOT NULL, DEFAULT 'text' |
| content | TEXT | 消息内容 | NOT NULL |
| extra_data | JSON | 扩展数据 | NULL |
| send_time | DATETIME(3) | 发送时间 | NOT NULL |
| status | TINYINT | 消息状态 | NOT NULL, DEFAULT 1 |
| create_time | DATETIME(3) | 创建时间 | NOT NULL |
| update_time | DATETIME(3) | 更新时间 | NOT NULL |
| deleted | TINYINT | 逻辑删除 | NOT NULL, DEFAULT 0 |

#### 消息类型枚举
- `text`: 文本消息
- `image`: 图片消息
- `file`: 文件消息
- `voice`: 语音消息
- `video`: 视频消息
- `system`: 系统消息

#### 消息状态枚举
- `0`: 发送中
- `1`: 已发送
- `2`: 已送达
- `3`: 已读
- `-1`: 发送失败

#### 索引设计
```sql
-- 主键索引
PRIMARY KEY (id)

-- 唯一索引
UNIQUE KEY uk_message_id (message_id)

-- 复合索引 - 私聊消息查询
KEY idx_private_chat (sender_id, receiver_id, send_time DESC)

-- 复合索引 - 群聊消息查询
KEY idx_room_chat (room_id, send_time DESC)

-- 索引 - 发送者消息查询
KEY idx_sender_time (sender_id, send_time DESC)

-- 索引 - 消息状态查询
KEY idx_status (status)

-- 索引 - 逻辑删除
KEY idx_deleted (deleted)
```

### 2. 聊天房间表 (chat_room)

#### 表结构
```sql
CREATE TABLE chat_room (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    room_id VARCHAR(64) NOT NULL UNIQUE COMMENT '房间ID（唯一标识）',
    room_name VARCHAR(100) NOT NULL COMMENT '房间名称',
    description VARCHAR(500) NULL COMMENT '房间描述',
    room_type VARCHAR(20) NOT NULL DEFAULT 'public' COMMENT '房间类型',
    creator_id BIGINT NOT NULL COMMENT '房间创建者ID',
    max_users INT NOT NULL DEFAULT 100 COMMENT '最大用户数',
    current_users INT NOT NULL DEFAULT 0 COMMENT '当前用户数',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '房间状态',
    config JSON NULL COMMENT '房间配置（JSON格式）',
    create_time DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    update_time DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
    deleted TINYINT NOT NULL DEFAULT 0 COMMENT '逻辑删除标志'
);
```

#### 字段说明
| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | BIGINT | 主键ID | PRIMARY KEY, AUTO_INCREMENT |
| room_id | VARCHAR(64) | 房间唯一标识 | NOT NULL, UNIQUE |
| room_name | VARCHAR(100) | 房间名称 | NOT NULL |
| description | VARCHAR(500) | 房间描述 | NULL |
| room_type | VARCHAR(20) | 房间类型 | NOT NULL, DEFAULT 'public' |
| creator_id | BIGINT | 创建者用户ID | NOT NULL |
| max_users | INT | 最大用户数 | NOT NULL, DEFAULT 100 |
| current_users | INT | 当前用户数 | NOT NULL, DEFAULT 0 |
| status | TINYINT | 房间状态 | NOT NULL, DEFAULT 1 |
| config | JSON | 房间配置 | NULL |

#### 房间类型枚举
- `public`: 公开房间
- `private`: 私有房间
- `group`: 群组房间

#### 房间状态枚举
- `0`: 禁用
- `1`: 启用

#### 索引设计
```sql
-- 主键索引
PRIMARY KEY (id)

-- 唯一索引
UNIQUE KEY uk_room_id (room_id)

-- 索引 - 创建者查询
KEY idx_creator (creator_id)

-- 索引 - 房间类型查询
KEY idx_room_type (room_type)

-- 索引 - 房间状态查询
KEY idx_status (status)

-- 索引 - 房间名称搜索
KEY idx_room_name (room_name)

-- 索引 - 逻辑删除
KEY idx_deleted (deleted)
```

### 3. 房间成员表 (chat_room_member)

#### 表结构
```sql
CREATE TABLE chat_room_member (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    room_id VARCHAR(64) NOT NULL COMMENT '房间ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    role VARCHAR(20) NOT NULL DEFAULT 'member' COMMENT '成员角色',
    join_time DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '加入时间',
    last_read_time DATETIME(3) NULL COMMENT '最后阅读时间',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '成员状态',
    create_time DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    update_time DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
    deleted TINYINT NOT NULL DEFAULT 0 COMMENT '逻辑删除标志'
);
```

#### 成员角色枚举
- `owner`: 房主
- `admin`: 管理员
- `member`: 普通成员

#### 成员状态枚举
- `0`: 已离开
- `1`: 正常
- `2`: 被禁言
- `3`: 被踢出

#### 索引设计
```sql
-- 主键索引
PRIMARY KEY (id)

-- 唯一索引 - 房间用户唯一性
UNIQUE KEY uk_room_user (room_id, user_id)

-- 索引 - 用户房间查询
KEY idx_user_rooms (user_id, join_time DESC)

-- 索引 - 房间成员查询
KEY idx_room_members (room_id, join_time)

-- 索引 - 成员角色查询
KEY idx_member_role (role)
```

### 4. 用户好友表 (chat_friend)

#### 表结构
```sql
CREATE TABLE chat_friend (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    friend_id BIGINT NOT NULL COMMENT '好友ID',
    friend_alias VARCHAR(50) NULL COMMENT '好友备注名',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '好友状态',
    create_time DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    update_time DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
    deleted TINYINT NOT NULL DEFAULT 0 COMMENT '逻辑删除标志'
);
```

#### 好友状态枚举
- `0`: 已删除
- `1`: 正常
- `2`: 已拉黑

#### 索引设计
```sql
-- 主键索引
PRIMARY KEY (id)

-- 唯一索引 - 用户好友唯一性
UNIQUE KEY uk_user_friend (user_id, friend_id)

-- 索引 - 好友关系查询
KEY idx_friend_relation (friend_id, user_id)

-- 索引 - 好友状态查询
KEY idx_friend_status (status)
```

### 5. 消息已读状态表 (chat_message_read)

#### 表结构
```sql
CREATE TABLE chat_message_read (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    message_id VARCHAR(64) NOT NULL COMMENT '消息ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    read_time DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '阅读时间',
    create_time DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间'
);
```

#### 索引设计
```sql
-- 主键索引
PRIMARY KEY (id)

-- 唯一索引 - 消息用户已读唯一性
UNIQUE KEY uk_message_user_read (message_id, user_id)

-- 索引 - 用户已读消息查询
KEY idx_user_read (user_id, read_time DESC)

-- 索引 - 消息已读统计
KEY idx_message_read (message_id)
```

### 6. 文件信息表 (chat_file)

#### 表结构
```sql
CREATE TABLE chat_file (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    file_id VARCHAR(64) NOT NULL UNIQUE COMMENT '文件ID',
    original_name VARCHAR(255) NOT NULL COMMENT '原始文件名',
    file_name VARCHAR(255) NOT NULL COMMENT '存储文件名',
    file_path VARCHAR(500) NOT NULL COMMENT '文件路径',
    file_size BIGINT NOT NULL COMMENT '文件大小（字节）',
    file_type VARCHAR(50) NOT NULL COMMENT '文件类型',
    mime_type VARCHAR(100) NOT NULL COMMENT 'MIME类型',
    uploader_id BIGINT NOT NULL COMMENT '上传者ID',
    upload_time DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '上传时间',
    download_count INT NOT NULL DEFAULT 0 COMMENT '下载次数',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态',
    create_time DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    update_time DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
    deleted TINYINT NOT NULL DEFAULT 0 COMMENT '逻辑删除标志'
);
```

#### 文件状态枚举
- `0`: 已删除
- `1`: 正常

#### 索引设计
```sql
-- 主键索引
PRIMARY KEY (id)

-- 唯一索引
UNIQUE KEY uk_file_id (file_id)

-- 索引 - 上传者文件查询
KEY idx_uploader_files (uploader_id, upload_time DESC)

-- 索引 - 文件类型查询
KEY idx_file_type (file_type)

-- 索引 - 文件状态查询
KEY idx_file_status (status)
```

## 🔗 表关系设计

### ER 图关系
```
chat_message ──┐
               ├── sender_id ──→ user (外部用户表)
               ├── receiver_id ──→ user (外部用户表)
               └── room_id ──→ chat_room

chat_room ──┐
            ├── creator_id ──→ user (外部用户表)
            └── room_id ←── chat_room_member

chat_room_member ──┐
                   ├── room_id ──→ chat_room
                   └── user_id ──→ user (外部用户表)

chat_friend ──┐
              ├── user_id ──→ user (外部用户表)
              └── friend_id ──→ user (外部用户表)

chat_message_read ──┐
                    ├── message_id ──→ chat_message
                    └── user_id ──→ user (外部用户表)

chat_file ──┐
            └── uploader_id ──→ user (外部用户表)
```

### 外键约束
```sql
-- 消息表外键约束
ALTER TABLE chat_message 
ADD CONSTRAINT fk_message_room 
FOREIGN KEY (room_id) REFERENCES chat_room(room_id) ON DELETE SET NULL;

-- 房间成员表外键约束
ALTER TABLE chat_room_member 
ADD CONSTRAINT fk_member_room 
FOREIGN KEY (room_id) REFERENCES chat_room(room_id) ON DELETE CASCADE;

-- 消息已读表外键约束
ALTER TABLE chat_message_read 
ADD CONSTRAINT fk_read_message 
FOREIGN KEY (message_id) REFERENCES chat_message(message_id) ON DELETE CASCADE;
```

## 📈 性能优化

### 分区策略
```sql
-- 按时间分区聊天消息表（按月分区）
ALTER TABLE chat_message 
PARTITION BY RANGE (YEAR(create_time) * 100 + MONTH(create_time)) (
    PARTITION p202401 VALUES LESS THAN (202402),
    PARTITION p202402 VALUES LESS THAN (202403),
    PARTITION p202403 VALUES LESS THAN (202404),
    -- ... 更多分区
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

### 查询优化建议

#### 1. 私聊历史查询优化
```sql
-- 使用复合索引优化私聊查询
SELECT * FROM chat_message 
WHERE ((sender_id = ? AND receiver_id = ?) OR (sender_id = ? AND receiver_id = ?))
  AND deleted = 0
ORDER BY send_time DESC 
LIMIT 20;
```

#### 2. 群聊历史查询优化
```sql
-- 使用房间ID索引优化群聊查询
SELECT * FROM chat_message 
WHERE room_id = ? AND deleted = 0
ORDER BY send_time DESC 
LIMIT 20;
```

#### 3. 未读消息统计优化
```sql
-- 使用子查询优化未读消息统计
SELECT COUNT(*) FROM chat_message m
WHERE m.receiver_id = ? 
  AND m.deleted = 0
  AND NOT EXISTS (
    SELECT 1 FROM chat_message_read r 
    WHERE r.message_id = m.message_id AND r.user_id = ?
  );
```

## 🔧 数据维护

### 数据清理策略
```sql
-- 清理过期的已删除消息（30天前）
DELETE FROM chat_message 
WHERE deleted = 1 
  AND update_time < DATE_SUB(NOW(), INTERVAL 30 DAY);

-- 清理过期的文件记录（90天前）
DELETE FROM chat_file 
WHERE status = 0 
  AND update_time < DATE_SUB(NOW(), INTERVAL 90 DAY);
```

### 数据归档策略
```sql
-- 归档历史消息（1年前的数据）
CREATE TABLE chat_message_archive LIKE chat_message;

INSERT INTO chat_message_archive 
SELECT * FROM chat_message 
WHERE create_time < DATE_SUB(NOW(), INTERVAL 1 YEAR);

DELETE FROM chat_message 
WHERE create_time < DATE_SUB(NOW(), INTERVAL 1 YEAR);
```

## 📊 监控指标

### 表大小监控
```sql
-- 查看各表的大小和行数
SELECT 
    table_name,
    table_rows,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size(MB)'
FROM information_schema.tables 
WHERE table_schema = 'springcloud_chat'
ORDER BY (data_length + index_length) DESC;
```

### 索引使用情况
```sql
-- 查看索引使用统计
SELECT 
    table_name,
    index_name,
    cardinality,
    sub_part,
    packed,
    nullable,
    index_type
FROM information_schema.statistics 
WHERE table_schema = 'springcloud_chat'
ORDER BY table_name, seq_in_index;
```
