package com.xiang.chat.config;

import io.github.resilience4j.circuitbreaker.CircuitBreaker;
import io.github.resilience4j.circuitbreaker.CircuitBreakerConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;

/**
 * Resilience4j配置类
 * 确保Feign调用使用信号量隔离策略，避免ThreadLocal问题
 */
@Slf4j
@Configuration
public class Resilience4jConfig {

    /**
     * 配置断路器 - 用于auth-service
     */
    @Bean
    public CircuitBreaker authServiceCircuitBreaker() {
        CircuitBreakerConfig config = CircuitBreakerConfig.custom()
                .failureRateThreshold(50.0f)                    // 失败率阈值50%
                .minimumNumberOfCalls(10)                       // 最小调用次数
                .slidingWindowSize(20)                          // 滑动窗口大小
                .waitDurationInOpenState(Duration.ofSeconds(10)) // 断路器打开状态等待时间
                .permittedNumberOfCallsInHalfOpenState(5)       // 半开状态允许的调用次数
                .automaticTransitionFromOpenToHalfOpenEnabled(true) // 自动从打开状态转换到半开状态
                .recordExceptions(Exception.class)              // 记录所有异常
                .build();

        CircuitBreaker circuitBreaker = CircuitBreaker.of("auth-service", config);
        
        // 添加事件监听器用于调试
        circuitBreaker.getEventPublisher()
                .onStateTransition(event -> 
                    log.info("Auth-service circuit breaker state transition: {} -> {}", 
                            event.getStateTransition().getFromState(), 
                            event.getStateTransition().getToState()));
        
        return circuitBreaker;
    }


}
