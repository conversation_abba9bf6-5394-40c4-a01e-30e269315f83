package com.xiang.chat.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.JwtDecoder;
import org.springframework.security.oauth2.jwt.NimbusJwtDecoder;
import org.springframework.security.web.SecurityFilterChain;

import jakarta.annotation.PostConstruct;

/**
 * Spring Security 安全配置 - OAuth2 Resource Server模式
 */
@Slf4j
@Configuration
@EnableWebSecurity
public class SecurityConfig {

    @Value("${spring.security.oauth2.resourceserver.jwt.jwk-set-uri:http://localhost:9000/oauth2/jwks}")
    private String jwkSetUri;

    /**
     * 配置SecurityContext传播策略
     * 确保子线程可以继承父线程的SecurityContext
     */
    @PostConstruct
    public void configureSecurityContextStrategy() {
        // 设置SecurityContext传播策略为INHERITABLE_THREAD_LOCAL
        // 这样子线程可以继承父线程的SecurityContext
        SecurityContextHolder.setStrategyName(SecurityContextHolder.MODE_INHERITABLETHREADLOCAL);
        log.info("SecurityContext传播策略设置为: MODE_INHERITABLETHREADLOCAL");
    }

    /**
     * API安全配置 - 使用OAuth2 Resource Server (JWT)
     * 优先级高，处理API请求
     */
    @Bean
    @Order(1)
    public SecurityFilterChain apiSecurityFilterChain(HttpSecurity http) throws Exception {
        http
                .securityMatcher("/api/**")  // 只处理API请求
                .csrf(AbstractHttpConfigurer::disable)
                .authorizeHttpRequests(authz -> authz
                        // 允许访问注册API
                        .requestMatchers("/api/register").permitAll()
                        // 其他API需要JWT认证
                        .anyRequest().authenticated())
                // 配置OAuth2资源服务器
                .oauth2ResourceServer(oauth2 -> oauth2
                        .jwt(jwt -> jwt.decoder(jwtDecoder())));

        log.info("API Security配置完成 - OAuth2 Resource Server (JWT)");
        return http.build();
    }

    /**
     * Web页面安全配置 - 使用表单登录
     * 优先级低，处理Web页面请求
     */
    @Bean
    @Order(2)
    public SecurityFilterChain webSecurityFilterChain(HttpSecurity http) throws Exception {
        http
                .csrf(AbstractHttpConfigurer::disable)
                .authorizeHttpRequests(authz -> authz
                        // 允许访问登录页面和静态资源
                        .requestMatchers("/", "/login", "/register").permitAll()
                        .requestMatchers("/static/**", "/css/**", "/js/**", "/images/**", "/files/**").permitAll()
                        .requestMatchers("/doc.html", "/swagger-ui/**", "/v3/api-docs/**", "/webjars/**").permitAll()
                        .requestMatchers("/actuator/**", "/health", "/info", "/metrics").permitAll()
                        .requestMatchers("/druid/**").permitAll()
                        .requestMatchers("/test/**").permitAll()
                        // 聊天页面需要认证
                        .requestMatchers("/chat").authenticated()
                        // 其他请求需要认证
                        .anyRequest().authenticated())
                // 配置表单登录
                .formLogin(form -> form
                        .loginPage("/login")
                        .defaultSuccessUrl("/chat", true)
                        .failureUrl("/login?error=true")
                        .permitAll())
                // 配置登出
                .logout(logout -> logout
                        .logoutSuccessUrl("/login?logout=true")
                        .permitAll());

        log.info("Web Security配置完成 - 表单登录");
        return http.build();
    }

    @Bean
    public JwtDecoder jwtDecoder() {
        return NimbusJwtDecoder.withJwkSetUri(jwkSetUri).build();
    }
}
