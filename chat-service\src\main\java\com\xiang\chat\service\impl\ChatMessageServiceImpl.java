package com.xiang.chat.service.impl;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import com.xiang.chat.dto.WebSocketMessage;
import com.xiang.chat.mapper.ChatMessageMapper;
import com.xiang.chat.model.dto.ChatMessage;
import com.xiang.chat.model.entity.ChatMessageEntity;
import com.xiang.chat.service.ChatMessageService;
import com.xiang.chat.service.ConnectionManager;
import com.xiang.chat.service.DistributedConnectionManager;
import com.xiang.chat.service.OfflineMessageService;
import com.xiang.chat.service.UserInfoService;
import com.xiang.chat.dto.UserInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * 聊天消息服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ChatMessageServiceImpl implements ChatMessageService {

    private final ChatMessageMapper chatMessageMapper;
    private final ConnectionManager connectionManager;
    private final DistributedConnectionManager distributedConnectionManager;
    private final RedisTemplate<String, Object> redisTemplate;
    private final OfflineMessageService offlineMessageService;
    private final UserInfoService userInfoService;

    @Override
    @Transactional
    public void handleChatMessage(ChatMessage chatMessage) {
        try {
            // 生成消息ID
            if (chatMessage.getMessageId() == null) {
                chatMessage.setMessageId(IdUtil.fastSimpleUUID());
            }

            // 处理引用消息
            if (chatMessage.isReplyMessage()) {
                processReferencedMessage(chatMessage);
            }

            // 设置消息状态为已发送
            chatMessage.setStatus(1);

            // 保存消息到数据库
            saveMessage(chatMessage);

            // 根据消息类型进行处理
            if (chatMessage.isPrivateMessage()) {
                sendPrivateMessage(chatMessage);
            } else if (chatMessage.isGroupMessage()) {
                sendGroupMessage(chatMessage);
            } else {
                log.warn("无效的聊天消息类型: {}", chatMessage);
                return;
            }

            // 缓存最近消息
            cacheRecentMessage(chatMessage);

            log.info("聊天消息处理完成: messageId={}", chatMessage.getMessageId());

        } catch (Exception e) {
            log.error("处理聊天消息失败: {}", chatMessage, e);
            throw e;
        }
    }

    @Override
    public void sendPrivateMessage(ChatMessage chatMessage) {
        // 获取发送者信息
        UserInfo senderInfo = userInfoService.getUserInfo(chatMessage.getSenderId());
        String senderName = senderInfo != null ?
            (senderInfo.getNickname() != null ? senderInfo.getNickname() : senderInfo.getUsername()) :
            "用户" + chatMessage.getSenderId();

        // 构建WebSocket消息数据
        Map<String, Object> messageData = new HashMap<>();
        messageData.put("messageId", chatMessage.getMessageId());
        messageData.put("senderId", chatMessage.getSenderId());
        messageData.put("senderName", senderName);  // 添加发送者名称
        messageData.put("receiverId", chatMessage.getReceiverId());
        messageData.put("messageType", chatMessage.getMessageType());
        messageData.put("content", chatMessage.getContent());
        messageData.put("sendTime", chatMessage.getSendTime());
        
        // 添加引用消息信息
        if (chatMessage.isReplyMessage()) {
            messageData.put("referenceMessageId", chatMessage.getReferenceMessageId());
            messageData.put("referenceContent", chatMessage.getReferenceContent());
            messageData.put("referenceSenderId", chatMessage.getReferenceSenderId());
        }
        
        // 如果有扩展数据（如文件信息），解析并添加到消息数据中
        if (chatMessage.getExtraData() != null && !chatMessage.getExtraData().trim().isEmpty()) {
            try {
                @SuppressWarnings("unchecked")
                Map<String, Object> extraData = JSON.parseObject(chatMessage.getExtraData(), Map.class);
                messageData.putAll(extraData);
            } catch (Exception e) {
                log.warn("解析extraData失败: {}", chatMessage.getExtraData(), e);
            }
        }
        
        WebSocketMessage wsMessage = WebSocketMessage.builder()
                .type("private_message")
                .data(messageData)
                .timestamp(System.currentTimeMillis())
                .build();

        // 使用分布式连接管理器发送给接收者（支持跨节点）
        boolean sentToReceiver = distributedConnectionManager.sendMessageToUserGlobally(chatMessage.getReceiverId(), wsMessage);
        
        // 同时发送给发送者（用于多端同步）
        boolean sentToSender = distributedConnectionManager.sendMessageToUserGlobally(chatMessage.getSenderId(), wsMessage);

        if (sentToReceiver) {
            log.info("私聊消息发送给接收者成功: messageId={}, receiverId={}",
                chatMessage.getMessageId(), chatMessage.getReceiverId());
        } else {
            log.info("私聊消息发送给接收者失败，直接存储为离线消息: messageId={}, receiverId={}",
                chatMessage.getMessageId(), chatMessage.getReceiverId());
            
            // 直接存储离线消息，不依赖消息队列
            try {
                offlineMessageService.storeOfflineChatMessage(chatMessage.getReceiverId(), chatMessage);
                log.info("离线消息存储成功: messageId={}, receiverId={}", 
                    chatMessage.getMessageId(), chatMessage.getReceiverId());
            } catch (Exception e) {
                log.error("离线消息存储失败: messageId={}, receiverId={}", 
                    chatMessage.getMessageId(), chatMessage.getReceiverId(), e);
            }
        }
        
        if (sentToSender) {
            log.info("私聊消息发送给发送者成功（多端同步）: messageId={}, senderId={}",
                chatMessage.getMessageId(), chatMessage.getSenderId());
        } else {
            log.debug("私聊消息发送给发送者失败（多端同步）: messageId={}, senderId={}",
                chatMessage.getMessageId(), chatMessage.getSenderId());
        }
    }

    @Override
    public void sendGroupMessage(ChatMessage chatMessage) {
        // 获取发送者信息
        UserInfo senderInfo = userInfoService.getUserInfo(chatMessage.getSenderId());
        String senderName = senderInfo != null ?
            (senderInfo.getNickname() != null ? senderInfo.getNickname() : senderInfo.getUsername()) :
            "用户" + chatMessage.getSenderId();

        // 构建WebSocket消息数据
        Map<String, Object> messageData = new HashMap<>();
        messageData.put("messageId", chatMessage.getMessageId());
        messageData.put("senderId", chatMessage.getSenderId());
        messageData.put("senderName", senderName);  // 添加发送者名称
        messageData.put("roomId", chatMessage.getRoomId());
        messageData.put("messageType", chatMessage.getMessageType());
        messageData.put("content", chatMessage.getContent());
        messageData.put("sendTime", chatMessage.getSendTime());
        
        // 添加引用消息信息
        if (chatMessage.isReplyMessage()) {
            messageData.put("referenceMessageId", chatMessage.getReferenceMessageId());
            messageData.put("referenceContent", chatMessage.getReferenceContent());
            messageData.put("referenceSenderId", chatMessage.getReferenceSenderId());
        }
        
        // 如果有扩展数据（如文件信息），解析并添加到消息数据中
        if (chatMessage.getExtraData() != null && !chatMessage.getExtraData().trim().isEmpty()) {
            try {
                @SuppressWarnings("unchecked")
                Map<String, Object> extraData = JSON.parseObject(chatMessage.getExtraData(), Map.class);
                messageData.putAll(extraData);
            } catch (Exception e) {
                log.warn("解析extraData失败: {}", chatMessage.getExtraData(), e);
            }
        }
        
        WebSocketMessage wsMessage = WebSocketMessage.builder()
                .type("group_message")
                .data(messageData)
                .timestamp(System.currentTimeMillis())
                .messageId(chatMessage.getMessageId())
                .senderId(chatMessage.getSenderId())
                .roomId(chatMessage.getRoomId())
                .build();

        // 使用分布式连接管理器广播给房间内所有用户（包括发送者，支持跨节点）
        int sentCount = distributedConnectionManager.broadcastToRoomGlobally(chatMessage.getRoomId(), wsMessage, null);

        // 处理房间内离线用户的消息存储
        handleOfflineUsersInRoom(chatMessage.getRoomId(), chatMessage, wsMessage);

        log.info("群聊消息广播完成: messageId={}, roomId={}, sentCount={}",
            chatMessage.getMessageId(), chatMessage.getRoomId(), sentCount);
    }

    /**
     * 处理房间内离线用户的消息存储
     */
    private void handleOfflineUsersInRoom(String roomId, ChatMessage chatMessage, WebSocketMessage wsMessage) {
        try {
            // 获取房间内所有用户（这里需要从房间服务获取）
            // 简化实现：从连接管理器获取在线用户，然后处理离线用户
            Set<Long> onlineUsers = connectionManager.getRoomUsers(roomId);

            // 这里应该从房间服务获取房间的所有成员
            // 然后找出离线的用户，为他们存储离线消息
            // 由于当前架构限制，暂时跳过离线用户处理
            // 在实际项目中，应该：
            // 1. 从ChatRoomService获取房间所有成员
            // 2. 对比在线用户列表，找出离线用户
            // 3. 为离线用户存储群组消息

            log.debug("房间消息离线用户处理: roomId={}, onlineUsers={}", roomId, onlineUsers.size());

        } catch (Exception e) {
            log.error("处理房间离线用户消息失败: roomId={}", roomId, e);
        }
    }

    @Override
    @Transactional
    public ChatMessageEntity saveMessage(ChatMessage chatMessage) {
        ChatMessageEntity entity = ChatMessageEntity.builder()
                .messageId(chatMessage.getMessageId())
                .senderId(chatMessage.getSenderId())
                .receiverId(chatMessage.getReceiverId())
                .roomId(chatMessage.getRoomId())
                .messageType(chatMessage.getMessageType())
                .content(chatMessage.getContent())
                .extraData(chatMessage.getExtraData())
                .sendTime(LocalDateTime.now())
                .status(chatMessage.getStatus())
                .referenceMessageId(chatMessage.getReferenceMessageId())
                .build();

        chatMessageMapper.insert(entity);
        return entity;
    }

    @Override
    public List<ChatMessageEntity> getChatHistory(String roomId, Long userId, int page, int size) {
        if (roomId != null && !roomId.trim().isEmpty()) {
            // 房间聊天历史
            return getRoomMessagesWithTimeRange(roomId, page, size, null);
        } else if (userId != null) {
            // 私聊历史（需要当前用户ID作为参数）
            // 这里简化处理，实际应该传入当前用户ID
            return getPrivateChatHistory(userId, userId, page, size);
        }
        return List.of();
    }

    /**
     * 获取带有完整引用信息的聊天历史
     */
    @Override
    public List<ChatMessage> getChatHistoryWithReferenceInfo(String roomId, Long userId, int page, int size) {
        List<ChatMessageEntity> entities = getChatHistory(roomId, userId, page, size);
        return entities.stream()
                .map(this::convertEntityToDtoWithReference)
                .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 将实体转换为DTO并填充引用信息
     */
    private ChatMessage convertEntityToDtoWithReference(ChatMessageEntity entity) {
        ChatMessage chatMessage = convertEntityToDto(entity);

        // 如果有引用消息，获取引用消息的详细信息
        if (entity.getReferenceMessageId() != null && !entity.getReferenceMessageId().trim().isEmpty()) {
            ChatMessageEntity referencedEntity = getReferencedMessage(entity.getReferenceMessageId());
            if (referencedEntity != null) {
                chatMessage.setReferenceContent(generateReferenceContent(referencedEntity));
                chatMessage.setReferenceSenderId(referencedEntity.getSenderId());
            }
        }

        return chatMessage;
    }

    /**
     * 获取房间消息（支持时间范围查询）
     */
    public List<ChatMessageEntity> getRoomMessagesWithTimeRange(String roomId, int page, int size, Long beforeTimestamp) {
        Page<ChatMessageEntity> pageParam = new Page<>(page, size);
        LambdaQueryWrapper<ChatMessageEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ChatMessageEntity::getRoomId, roomId)
                .ne(ChatMessageEntity::getMessageType, "system") // 排除系统消息
                .eq(ChatMessageEntity::getStatus, 1); // 只查询正常状态的消息

        // 如果指定了时间戳，查询该时间之前的消息
        if (beforeTimestamp != null) {
            wrapper.lt(ChatMessageEntity::getSendTime,
                LocalDateTime.ofInstant(java.time.Instant.ofEpochMilli(beforeTimestamp),
                    java.time.ZoneId.systemDefault()));
        }

        wrapper.orderByDesc(ChatMessageEntity::getSendTime);

        Page<ChatMessageEntity> result = chatMessageMapper.selectPage(pageParam, wrapper);
        List<ChatMessageEntity> messages = result.getRecords();

        // 缓存查询结果
        cacheRoomMessages(roomId, messages);

        return messages;
    }

    /**
     * 获取房间最新消息
     */
    public List<ChatMessageEntity> getRoomLatestMessages(String roomId, int limit) {
        // 先尝试从缓存获取
        List<ChatMessageEntity> cachedMessages = getCachedRoomMessages(roomId);
        if (cachedMessages != null && !cachedMessages.isEmpty()) {
            return cachedMessages.stream()
                .limit(limit)
                .collect(java.util.stream.Collectors.toList());
        }

        // 从数据库查询
        LambdaQueryWrapper<ChatMessageEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ChatMessageEntity::getRoomId, roomId)
                .ne(ChatMessageEntity::getMessageType, "system")
                .eq(ChatMessageEntity::getStatus, 1)
                .orderByDesc(ChatMessageEntity::getSendTime)
                .last("LIMIT " + limit);

        List<ChatMessageEntity> messages = chatMessageMapper.selectList(wrapper);

        // 缓存结果
        cacheRoomMessages(roomId, messages);

        return messages;
    }

    /**
     * 缓存房间消息
     */
    private void cacheRoomMessages(String roomId, List<ChatMessageEntity> messages) {
        try {
            if (messages == null || messages.isEmpty()) {
                return;
            }

            String cacheKey = "chat:room:messages:" + roomId;
            redisTemplate.opsForValue().set(cacheKey, JSON.toJSONString(messages), 10, TimeUnit.MINUTES);

        } catch (Exception e) {
            log.error("缓存房间消息失败: roomId={}", roomId, e);
        }
    }

    /**
     * 获取缓存的房间消息
     */
    private List<ChatMessageEntity> getCachedRoomMessages(String roomId) {
        try {
            String cacheKey = "chat:room:messages:" + roomId;
            Object cached = redisTemplate.opsForValue().get(cacheKey);

            if (cached != null) {
                return JSON.parseArray(cached.toString(), ChatMessageEntity.class);
            }

        } catch (Exception e) {
            log.error("获取缓存房间消息失败: roomId={}", roomId, e);
        }

        return null;
    }

    @Override
    public List<ChatMessageEntity> getPrivateChatHistory(Long senderId, Long receiverId, int page, int size) {
        Page<ChatMessageEntity> pageParam = new Page<>(page, size);
        LambdaQueryWrapper<ChatMessageEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.and(w -> w.eq(ChatMessageEntity::getSenderId, senderId)
                        .eq(ChatMessageEntity::getReceiverId, receiverId))
                .or(w -> w.eq(ChatMessageEntity::getSenderId, receiverId)
                        .eq(ChatMessageEntity::getReceiverId, senderId))
                .orderByDesc(ChatMessageEntity::getSendTime);

        Page<ChatMessageEntity> result = chatMessageMapper.selectPage(pageParam, wrapper);
        return result.getRecords();
    }

    @Override
    @Transactional
    public void markMessageAsRead(String messageId, Long userId) {
        LambdaQueryWrapper<ChatMessageEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ChatMessageEntity::getMessageId, messageId)
                .eq(ChatMessageEntity::getReceiverId, userId);

        ChatMessageEntity entity = new ChatMessageEntity();
        entity.setStatus(3); // 已读状态
        chatMessageMapper.update(entity, wrapper);

        log.info("消息标记为已读: messageId={}, userId={}", messageId, userId);
    }

    @Override
    public long getUnreadMessageCount(Long userId) {
        LambdaQueryWrapper<ChatMessageEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ChatMessageEntity::getReceiverId, userId)
                .in(ChatMessageEntity::getStatus, 1, 2); // 已发送或已送达

        return chatMessageMapper.selectCount(wrapper);
    }

    @Override
    @Transactional
    public void deleteMessage(String messageId, Long userId) {
        LambdaQueryWrapper<ChatMessageEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ChatMessageEntity::getMessageId, messageId)
                .eq(ChatMessageEntity::getSenderId, userId);

        chatMessageMapper.delete(wrapper);
        log.info("消息删除成功: messageId={}, userId={}", messageId, userId);
    }

    @Override
    @Transactional
    public void recallMessage(String messageId, Long userId) {
        LambdaQueryWrapper<ChatMessageEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ChatMessageEntity::getMessageId, messageId)
                .eq(ChatMessageEntity::getSenderId, userId);

        ChatMessageEntity entity = chatMessageMapper.selectOne(wrapper);
        if (entity == null) {
            throw new RuntimeException("消息不存在或无权限撤回");
        }

        // 检查撤回时间限制（例如：2分钟内可撤回）
        if (entity.getSendTime().isBefore(LocalDateTime.now().minusMinutes(2))) {
            throw new RuntimeException("消息发送超过2分钟，无法撤回");
        }

        // 更新消息内容为撤回提示
        entity.setContent("[消息已撤回]");
        entity.setMessageType("system");
        chatMessageMapper.updateById(entity);

        // 通知相关用户消息已撤回
        notifyMessageRecalled(entity);

        log.info("消息撤回成功: messageId={}, userId={}", messageId, userId);
    }



    /**
     * 缓存最近消息
     */
    private void cacheRecentMessage(ChatMessage chatMessage) {
        try {
            String key = chatMessage.isPrivateMessage() 
                    ? "chat:recent:private:" + Math.min(chatMessage.getSenderId(), chatMessage.getReceiverId()) 
                            + ":" + Math.max(chatMessage.getSenderId(), chatMessage.getReceiverId())
                    : "chat:recent:room:" + chatMessage.getRoomId();

            redisTemplate.opsForList().leftPush(key, JSON.toJSONString(chatMessage));
            redisTemplate.opsForList().trim(key, 0, 99); // 保留最近100条消息
            redisTemplate.expire(key, 7, TimeUnit.DAYS); // 7天过期
        } catch (Exception e) {
            log.error("缓存最近消息失败: messageId={}", chatMessage.getMessageId(), e);
        }
    }



    /**
     * 处理引用消息
     */
    private void processReferencedMessage(ChatMessage chatMessage) {
        if (chatMessage.getReferenceMessageId() == null || chatMessage.getReferenceMessageId().trim().isEmpty()) {
            return;
        }

        try {
            // 获取被引用的消息
            ChatMessageEntity referencedMessage = getReferencedMessage(chatMessage.getReferenceMessageId());
            if (referencedMessage == null) {
                log.warn("引用的消息不存在: referenceMessageId={}", chatMessage.getReferenceMessageId());
                // 清空引用信息
                chatMessage.setReferenceMessageId(null);
                chatMessage.setReferenceContent(null);
                chatMessage.setReferenceSenderId(null);
                return;
            }

            // 验证引用消息的权限
            if (!validateReferencePermission(chatMessage, referencedMessage)) {
                log.warn("无权限引用该消息: referenceMessageId={}, userId={}", 
                    chatMessage.getReferenceMessageId(), chatMessage.getSenderId());
                throw new RuntimeException("无权限引用该消息");
            }

            // 设置引用消息的内容摘要
            String referenceContent = generateReferenceContent(referencedMessage);
            chatMessage.setReferenceContent(referenceContent);
            chatMessage.setReferenceSenderId(referencedMessage.getSenderId());

            log.info("处理引用消息成功: messageId={}, referenceMessageId={}", 
                chatMessage.getMessageId(), chatMessage.getReferenceMessageId());

        } catch (Exception e) {
            log.error("处理引用消息失败: messageId={}, referenceMessageId={}", 
                chatMessage.getMessageId(), chatMessage.getReferenceMessageId(), e);
            throw e;
        }
    }

    /**
     * 验证引用消息的权限
     */
    private boolean validateReferencePermission(ChatMessage chatMessage, ChatMessageEntity referencedMessage) {
        // 私聊消息：只能引用同一对话中的消息
        if (chatMessage.isPrivateMessage()) {
            return (referencedMessage.getSenderId().equals(chatMessage.getSenderId()) && 
                    referencedMessage.getReceiverId().equals(chatMessage.getReceiverId())) ||
                   (referencedMessage.getSenderId().equals(chatMessage.getReceiverId()) && 
                    referencedMessage.getReceiverId().equals(chatMessage.getSenderId()));
        }
        
        // 群聊消息：只能引用同一房间中的消息
        if (chatMessage.isGroupMessage()) {
            return referencedMessage.getRoomId() != null && 
                   referencedMessage.getRoomId().equals(chatMessage.getRoomId());
        }
        
        return false;
    }

    /**
     * 生成引用消息的内容摘要
     */
    private String generateReferenceContent(ChatMessageEntity referencedMessage) {
        String content = referencedMessage.getContent();
        if (content == null || content.trim().isEmpty()) {
            // 根据消息类型生成默认摘要
            switch (referencedMessage.getMessageType()) {
                case "image":
                    return "[图片]";
                case "file":
                    return "[文件]";
                case "voice":
                    return "[语音]";
                case "video":
                    return "[视频]";
                default:
                    return "[消息]";
            }
        }
        
        // 限制摘要长度
        if (content.length() > 100) {
            return content.substring(0, 100) + "...";
        }
        
        return content;
    }

    /**
     * 获取被引用的消息
     */
    @Override
    public ChatMessageEntity getReferencedMessage(String messageId) {
        LambdaQueryWrapper<ChatMessageEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ChatMessageEntity::getMessageId, messageId);
        return chatMessageMapper.selectOne(wrapper);
    }

    /**
     * 获取消息的回复列表
     */
    @Override
    public List<ChatMessageEntity> getMessageReplies(String messageId, int page, int size) {
        Page<ChatMessageEntity> pageParam = new Page<>(page, size);
        LambdaQueryWrapper<ChatMessageEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ChatMessageEntity::getReferenceMessageId, messageId)
                .eq(ChatMessageEntity::getStatus, 1) // 只查询正常状态的消息
                .orderByAsc(ChatMessageEntity::getSendTime); // 按时间正序排列

        Page<ChatMessageEntity> result = chatMessageMapper.selectPage(pageParam, wrapper);
        return result.getRecords();
    }

    /**
     * 获取消息的完整信息（包括引用消息的详细信息）
     */
    @Override
    public ChatMessage getMessageWithReferenceInfo(String messageId) {
        ChatMessageEntity entity = getReferencedMessage(messageId);
        if (entity == null) {
            return null;
        }

        ChatMessage chatMessage = convertEntityToDto(entity);

        // 如果有引用消息，获取引用消息的详细信息
        if (entity.getReferenceMessageId() != null && !entity.getReferenceMessageId().trim().isEmpty()) {
            ChatMessageEntity referencedEntity = getReferencedMessage(entity.getReferenceMessageId());
            if (referencedEntity != null) {
                chatMessage.setReferenceContent(generateReferenceContent(referencedEntity));
                chatMessage.setReferenceSenderId(referencedEntity.getSenderId());
            }
        }

        return chatMessage;
    }

    /**
     * 将实体转换为DTO
     */
    private ChatMessage convertEntityToDto(ChatMessageEntity entity) {
        return ChatMessage.builder()
                .messageId(entity.getMessageId())
                .senderId(entity.getSenderId())
                .receiverId(entity.getReceiverId())
                .roomId(entity.getRoomId())
                .messageType(entity.getMessageType())
                .content(entity.getContent())
                .extraData(entity.getExtraData())
                .sendTime(entity.getSendTime().atZone(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli())
                .status(entity.getStatus())
                .referenceMessageId(entity.getReferenceMessageId())
                .build();
    }

    /**
     * 通知消息已撤回
     */
    private void notifyMessageRecalled(ChatMessageEntity entity) {
        WebSocketMessage wsMessage = WebSocketMessage.builder()
                .type("message_recalled")
                .data(Map.of(
                        "messageId", entity.getMessageId(),
                        "senderId", entity.getSenderId()
                ))
                .timestamp(System.currentTimeMillis())
                .build();

        if (entity.getReceiverId() != null) {
            // 私聊消息撤回通知
            connectionManager.sendMessageToUser(entity.getReceiverId(), wsMessage);
        } else if (entity.getRoomId() != null) {
            // 群聊消息撤回通知
            connectionManager.broadcastToRoom(entity.getRoomId(), wsMessage, entity.getSenderId());
        }
    }
}
