package com.xiang.chat.config;

import feign.Feign;
import feign.Request;
import feign.Retryer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.cloud.openfeign.FeignClientsConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.util.concurrent.TimeUnit;

/**
 * Feign ThreadLocal配置
 * 确保Feign调用在同一线程中执行，避免ThreadLocal丢失问题
 */
@Slf4j
@Configuration
@ConditionalOnClass(Feign.class)
public class FeignThreadLocalConfig extends FeignClientsConfiguration {

    /**
     * 配置Feign请求选项
     * 设置合理的超时时间，避免长时间阻塞
     */
    @Bean
    @Primary
    public Request.Options feignRequestOptions() {
        log.info("Configuring Feign Request Options for ThreadLocal compatibility");
        return new Request.Options(
                5000,  // 连接超时 5秒
                TimeUnit.MILLISECONDS,
                10000, // 读取超时 10秒
                TimeUnit.MILLISECONDS,
                false  // 不跟随重定向
        );
    }

    /**
     * 禁用重试机制
     * 重试可能导致线程切换，影响ThreadLocal
     */
    @Bean
    @Primary
    public Retryer feignRetryer() {
        log.info("Configuring Feign Retryer.NEVER_RETRY to avoid thread switching");
        return Retryer.NEVER_RETRY;
    }
}
