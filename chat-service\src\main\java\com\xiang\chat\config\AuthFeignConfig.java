package com.xiang.chat.config;

import com.xiang.chat.jwt.AuthFeignRequestInterceptor;
import feign.RequestInterceptor;
import feign.Retryer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Auth服务Feign配置
 * 为AuthFeignService配置JWT token拦截器和断路器策略
 */
@Slf4j
@Configuration
@RequiredArgsConstructor
public class AuthFeignConfig {

    private final AuthFeignRequestInterceptor authFeignRequestInterceptor;

    /**
     * 为auth-service配置请求拦截器
     * @return RequestInterceptor
     */
    @Bean
    public RequestInterceptor authServiceRequestInterceptor() {
        log.info("Configuring AuthFeignRequestInterceptor for auth-service");
        return authFeignRequestInterceptor;
    }

    /**
     * 禁用重试机制，避免线程切换导致ThreadLocal问题
     * @return Retryer
     */
    @Bean
    public Retryer authServiceRetryer() {
        log.info("Configuring Feign Retryer.NEVER_RETRY for auth-service to avoid thread switching");
        return Retryer.NEVER_RETRY;
    }
}