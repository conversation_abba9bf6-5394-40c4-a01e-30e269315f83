package com.xiang.chat.controller;

import com.xiang.chat.core.R;
import com.xiang.chat.service.FileUploadService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

/**
 * 文件上传下载控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/chat/files")
@RequiredArgsConstructor
@Tag(name = "文件管理", description = "聊天文件上传下载接口")
public class FileController {

    private final FileUploadService fileUploadService;

    @PostMapping("/upload/chat")
    @Operation(summary = "上传聊天文件")
    public R<FileUploadService.FileUploadResult> uploadChatFile(
            @Parameter(description = "文件") @RequestParam("file") MultipartFile file,
            @Parameter(description = "房间ID") @RequestParam(value = "roomId", required = false) String roomId,
            @Parameter(description = "用户ID") @RequestParam(value = "userId", required = false) Long userId,
            @AuthenticationPrincipal UserDetails userDetails) {

        try {
            // 优先从认证信息获取用户ID，如果没有则使用参数传递的用户ID
            Long actualUserId = userId;
            if (userDetails != null) {
                actualUserId = Long.valueOf(userDetails.getUsername());
            } else if (userId == null) {
                return R.error("用户ID不能为空");
            }

            FileUploadService.FileUploadResult result = fileUploadService.uploadChatFile(file, actualUserId, roomId);

            log.info("聊天文件上传成功: fileId={}, userId={}, roomId={}",
                    result.getFileId(), actualUserId, roomId);

            return R.success(result);

        } catch (Exception e) {
            log.error("聊天文件上传失败", e);
            return R.error("文件上传失败: " + e.getMessage());
        }
    }

    @PostMapping("/upload/avatar")
    @Operation(summary = "上传头像文件")
    public R<FileUploadService.FileUploadResult> uploadAvatarFile(
            @Parameter(description = "头像文件") @RequestParam("file") MultipartFile file,
            @Parameter(description = "用户ID") @RequestParam(value = "userId", required = false) Long userId,
            @AuthenticationPrincipal UserDetails userDetails) {

        try {
            // 优先从认证信息获取用户ID，如果没有则使用参数传递的用户ID
            Long actualUserId = userId;
            if (userDetails != null) {
                actualUserId = Long.valueOf(userDetails.getUsername());
            } else if (userId == null) {
                return R.error("用户ID不能为空");
            }

            FileUploadService.FileUploadResult result = fileUploadService.uploadAvatarFile(file, actualUserId);

            log.info("头像文件上传成功: fileId={}, userId={}", result.getFileId(), actualUserId);

            return R.success(result);

        } catch (Exception e) {
            log.error("头像文件上传失败", e);
            return R.error("头像上传失败: " + e.getMessage());
        }
    }

    @GetMapping("/{fileId}/download")
    @Operation(summary = "下载文件")
    public ResponseEntity<Resource> downloadFile(
            @Parameter(description = "文件ID") @PathVariable String fileId,
            @AuthenticationPrincipal UserDetails userDetails) {

        try {
            FileUploadService.FileInfo fileInfo = fileUploadService.getFileInfo(fileId);
            if (fileInfo == null || fileInfo.isDeleted()) {
                return ResponseEntity.notFound().build();
            }

            File file = new File(fileInfo.getFilePath());
            if (!file.exists()) {
                return ResponseEntity.notFound().build();
            }

            Resource resource = new FileSystemResource(file);

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.add(HttpHeaders.CONTENT_DISPOSITION,
                    "attachment; filename*=UTF-8''" +
                            URLEncoder.encode(fileInfo.getOriginalName(), StandardCharsets.UTF_8));
            headers.add(HttpHeaders.CONTENT_TYPE,
                    MediaType.APPLICATION_OCTET_STREAM_VALUE);
            headers.add(HttpHeaders.CONTENT_LENGTH, String.valueOf(file.length()));

            // 记录下载日志
            Long userId = null;
            if (userDetails != null) {
                try {
                    userId = Long.valueOf(userDetails.getUsername());
                } catch (NumberFormatException e) {
                    log.warn("无法解析用户ID: {}", userDetails.getUsername());
                }
            }
            log.info("文件下载: fileId={}, fileName={}, userId={}",
                    fileId, fileInfo.getOriginalName(), userId != null ? userId : "匿名用户");

            return ResponseEntity.ok()
                    .headers(headers)
                    .body(resource);

        } catch (Exception e) {
            log.error("文件下载失败: fileId={}", fileId, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    @GetMapping("/{fileId}/thumbnail")
    @Operation(summary = "获取文件缩略图")
    public ResponseEntity<Resource> getThumbnail(
            @Parameter(description = "文件ID") @PathVariable String fileId) {

        try {
            FileUploadService.FileInfo fileInfo = fileUploadService.getFileInfo(fileId);
            if (fileInfo == null || fileInfo.isDeleted() || fileInfo.getThumbnailUrl() == null) {
                return ResponseEntity.notFound().build();
            }

            // 从缩略图URL获取文件路径
            String thumbnailPath = getThumbnailPathFromUrl(fileInfo.getThumbnailUrl(), fileId);
            File thumbnailFile = new File(thumbnailPath);

            if (!thumbnailFile.exists()) {
                return ResponseEntity.notFound().build();
            }

            Resource resource = new FileSystemResource(thumbnailFile);

            return ResponseEntity.ok()
                    .contentType(MediaType.IMAGE_JPEG)
                    .body(resource);

        } catch (Exception e) {
            log.error("获取缩略图失败: fileId={}", fileId, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    @GetMapping("/{fileId}/info")
    @Operation(summary = "获取文件信息")
    public R<FileUploadService.FileInfo> getFileInfo(
            @Parameter(description = "文件ID") @PathVariable String fileId,
            @AuthenticationPrincipal UserDetails userDetails) {

        try {
            FileUploadService.FileInfo fileInfo = fileUploadService.getFileInfo(fileId);
            if (fileInfo == null) {
                return R.error("文件不存在");
            }

            if (fileInfo.isDeleted()) {
                return R.error("文件已被删除");
            }

            return R.success(fileInfo);

        } catch (Exception e) {
            log.error("获取文件信息失败: fileId={}", fileId, e);
            return R.error("获取文件信息失败: " + e.getMessage());
        }
    }

    @DeleteMapping("/{fileId}")
    @Operation(summary = "删除文件")
    public R<Boolean> deleteFile(
            @Parameter(description = "文件ID") @PathVariable String fileId,
            @AuthenticationPrincipal UserDetails userDetails) {

        try {
            Long userId = Long.valueOf(userDetails.getUsername());
            boolean success = fileUploadService.deleteFile(fileId, userId);

            if (success) {
                log.info("文件删除成功: fileId={}, userId={}", fileId, userId);
                return R.success(true);
            } else {
                return R.error("文件删除失败");
            }

        } catch (Exception e) {
            log.error("文件删除失败: fileId={}", fileId, e);
            return R.error("文件删除失败: " + e.getMessage());
        }
    }

    @GetMapping("/{fileId}/exists")
    @Operation(summary = "检查文件是否存在")
    public R<Boolean> fileExists(
            @Parameter(description = "文件ID") @PathVariable String fileId) {

        try {
            boolean exists = fileUploadService.fileExists(fileId);
            return R.success(exists);

        } catch (Exception e) {
            log.error("检查文件存在性失败: fileId={}", fileId, e);
            return R.error("检查文件失败: " + e.getMessage());
        }
    }

    /**
     * 从缩略图URL获取文件路径
     */
    private String getThumbnailPathFromUrl(String thumbnailUrl, String fileId) {
        // 这里需要根据实际的文件存储配置来实现
        // 简化实现，实际应该从配置中获取路径
        return System.getProperty("java.io.tmpdir") + "/chat/thumbnails/" + fileId + ".jpg";
    }
}
