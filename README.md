# SpringCloud 微服务聊天系统

基于 SpringCloud Alibaba + Netty + RocketMQ 的企业级分布式实时聊天系统

## 🚀 项目概述

这是一个完整的微服务架构聊天系统，采用现代化技术栈构建，支持高并发实时消息传递、多用户管理、房间管理、离线消息等企业级功能。系统具备高可用、高性能、可扩展的特点，适用于企业内部通信、客服系统、社交平台等多种场景。

### 项目特色
- 🏗️ **微服务架构**: 基于SpringCloud Alibaba的完整微服务生态
- 🚀 **高性能通信**: Netty WebSocket实现毫秒级实时消息推送
- 🔄 **分布式消息**: RocketMQ保证消息可靠传递和跨节点路由
- 💾 **多级存储**: MySQL持久化 + Redis缓存的高效数据存储
- 🔐 **安全认证**: OAuth2 + JWT的企业级安全认证体系
- 📊 **监控运维**: Prometheus + Micrometer的完整监控方案
- 🐳 **容器化部署**: Docker Compose一键部署，支持生产环境
- 🔧 **容错机制**: Resilience4j熔断器提供服务容错能力
- 🎯 **事务保证**: RocketMQ事务消息确保数据一致性
- 📝 **消息引用**: 支持消息引用回复和内容摘要功能

## 🏗️ 技术架构

### 核心技术栈
- **SpringCloud Alibaba 2023.0.1.2** - 微服务框架
- **SpringBoot 3.2.4** - 应用框架
- **Java 17** - 编程语言，支持最新语言特性
- **Nacos 2.0+** - 服务注册发现 & 配置中心
- **RocketMQ 4.9+** - 分布式消息队列
- **Netty 4.1.107** - 高性能网络通信框架
- **MyBatis-Plus 3.5.10.1** - 数据持久化框架
- **Redis 6.0+** - 缓存 & 分布式锁
- **MySQL 8.0+** - 关系型数据库
- **Spring Security 6.x** - 安全认证框架
- **OAuth2 + JWT** - 现代化认证授权方案
- **Resilience4j** - 熔断器和容错框架
- **Knife4j 4.4.0** - API文档生成工具
- **Druid 1.2.21** - 高性能数据库连接池
- **Hutool 5.8.22** - Java工具类库
- **FastJSON2 2.0.43** - 高性能JSON处理

### 系统架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web Client    │    │   Gateway       │    │   Auth Service  │    │   Chat Service  │
│   (前端界面)     │────│   (网关服务)     │────│   (认证服务)     │────│   (聊天服务)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │                       │
    WebSocket/HTTP           Load Balance            OAuth2/JWT              WebSocket/HTTP
         │                       │                       │                       │
         └───────────────────────┼───────────────────────┼───────────────────────┘
                                 │                       │
         ┌───────────────────────┼───────────────────────┼───────────────────────┐
         │                       │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│     Nacos       │    │   RocketMQ      │    │     Redis       │    │     MySQL       │
│ (注册中心/配置)   │    │   (消息队列)     │    │   (缓存/锁)     │    │   (数据存储)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │                       │
         └───────────────────────┼───────────────────────┼───────────────────────┘
                                 │                       │
                        ┌─────────────────┐    ┌─────────────────┐
                        │   Prometheus    │    │   File Storage  │
                        │   (监控指标)     │    │   (文件存储)     │
                        └─────────────────┘    └─────────────────┘
```

### 微服务模块
- **chat-service**: 聊天核心服务，提供WebSocket实时通信和REST API
- **auth-service**: 认证授权服务，提供OAuth2认证和用户管理
- **gateway**: API网关服务，提供路由转发和负载均衡
- **springcloud-alibaba-mybaits**: 数据访问层公共模块

### 分布式架构特点
- **多节点部署**: 支持聊天服务多实例部署，实现负载均衡
- **消息路由**: 基于RocketMQ实现跨节点消息转发
- **连接管理**: 分布式WebSocket连接管理，支持用户在不同节点间通信
- **故障转移**: 节点故障自动检测和连接迁移
- **配置中心**: Nacos统一配置管理，支持动态配置更新
- **服务发现**: 自动服务注册发现，支持服务健康检查
- **熔断降级**: Resilience4j熔断器，提供服务容错能力

## ✨ 核心功能

### 🔐 用户认证与管理
- [x] **OAuth2认证** - 基于OAuth2 + JWT的企业级认证体系
- [x] **用户注册登录** - 完整的用户注册、登录、密码管理功能
- [x] **用户状态管理** - 实时在线/离线状态跟踪，确保数量与列表一致性
- [x] **分布式会话** - 跨节点用户会话管理
- [x] **多设备支持** - 同一用户多设备同时在线
- [x] **权限控制** - 基于角色的访问控制(RBAC)
- [x] **自动房间加入** - 用户认证后自动加入默认房间

### 💬 实时聊天通信
- [x] **私聊功能** - 用户间一对一实时聊天
- [x] **群聊功能** - 房间内多人实时聊天
- [x] **消息类型** - 支持文本、文件、图片、语音、视频等多种消息类型
- [x] **实时推送** - 基于WebSocket的毫秒级消息推送
- [x] **消息确认** - 消息送达和已读状态确认
- [x] **离线消息** - 离线用户消息存储和上线推送
- [x] **消息历史** - 完整的聊天记录查询和分页
- [x] **引用回复** - 支持引用之前的消息进行回复，形成对话上下文
- [x] **链式回复** - 支持对回复消息再次回复，构建完整对话链
- [x] **异步广播** - 用户状态变更异步广播，避免主线程阻塞
- [x] **消息搜索** - 支持消息内容全文搜索

### 🏠 房间管理系统
- [x] **房间创建** - 支持创建公开、私有、群组等不同类型房间
- [x] **权限管理** - 房间管理员、成员权限控制
- [x] **成员管理** - 用户加入/离开房间，成员列表维护
- [x] **房间浏览** - 公开房间列表和搜索功能
- [x] **房间设置** - 房间信息修改、人数限制等
- [x] **默认房间** - 系统预设公共聊天室，用户自动加入
- [x] **房间密码** - 支持私有房间密码保护

### 📁 文件传输服务
- [x] **文件上传** - 支持多种文件类型上传(图片、文档、压缩包等)
- [x] **文件下载** - 安全的文件下载服务
- [x] **文件预览** - 图片文件在线预览
- [x] **文件管理** - 文件存储、清理、权限控制
- [x] **大小限制** - 可配置的文件大小和类型限制(默认10MB)
- [x] **文件安全** - 文件类型检测和恶意文件过滤

### 🔄 分布式架构特性
- [x] **多节点部署** - 支持水平扩展和负载均衡
- [x] **服务发现** - 基于Nacos的自动服务注册发现
- [x] **消息路由** - 跨节点消息智能路由和转发
- [x] **连接管理** - 分布式WebSocket连接池管理
- [x] **故障转移** - 节点故障自动检测和连接迁移
- [x] **负载均衡** - 连接和消息负载在多节点间均衡分布

### 🔄 分布式消息队列
- [x] **消息路由** - 基于RocketMQ的跨节点消息路由
- [x] **消息持久化** - 消息可靠存储，防止丢失
- [x] **消息顺序** - 保证消息的顺序性
- [x] **消息重试** - 失败消息自动重试机制
- [x] **消息监控** - 消息队列状态监控
- [x] **事务消息** - 支持事务性消息处理
- [x] **消息过滤** - 支持消息标签过滤和条件过滤

### 💾 数据持久化与缓存
- [x] **消息存储** - MySQL持久化存储所有聊天消息
- [x] **用户数据** - 用户信息和状态数据管理
- [x] **房间数据** - 房间信息和成员关系存储
- [x] **Redis缓存** - 热点数据缓存和分布式锁
- [x] **离线消息** - 离线消息队列和推送机制
- [x] **数据备份** - 定期数据备份和恢复机制

### 🔧 系统监控与运维
- [x] **健康检查** - 服务和连接健康状态监控
- [x] **心跳机制** - WebSocket连接心跳保活
- [x] **超时处理** - 连接超时自动清理和重连
- [x] **性能监控** - 连接数、消息量等关键指标统计
- [x] **日志管理** - 结构化日志记录和分析
- [x] **异常处理** - 全局异常捕获和处理机制

## 🚀 快速开始

### 环境要求
- **JDK 17+** - 支持最新Java特性
- **Maven 3.6+** - 项目构建工具
- **MySQL 8.0+** - 主数据库
- **Redis 6.0+** - 缓存和分布式锁
- **Nacos 2.0+** - 服务注册发现和配置中心
- **RocketMQ 4.9+** - 分布式消息队列

### 一键启动（推荐）

使用Docker Compose快速启动所有依赖服务：

```bash
# 克隆项目
git clone <repository-url>
cd springcloud-alibaba-chat-service

# 启动所有依赖服务
docker-compose up -d

# 等待服务启动完成（约30秒）
# 启动聊天服务
cd chat-service
mvn spring-boot:run
```

### 手动启动步骤

1. **启动基础服务**
   ```bash
   # 启动 MySQL
   docker run -d --name mysql \
     -p 3306:3306 \
     -e MYSQL_ROOT_PASSWORD=123456 \
     -e MYSQL_DATABASE=chat_db \
     mysql:8.0

   # 启动 Redis
   docker run -d --name redis -p 6379:6379 redis:6-alpine

   # 启动 Nacos
   docker run -d --name nacos \
     -p 8848:8848 \
     -e MODE=standalone \
     nacos/nacos-server:v2.0.4

   # 启动 RocketMQ
   docker run -d --name rmqnamesrv -p 9876:9876 foxiswho/rocketmq:4.8.0 sh mqnamesrv
   docker run -d --name rmqbroker -p 10909:10909 -p 10911:10911 \
     --link rmqnamesrv:namesrv \
     -e "NAMESRV_ADDR=namesrv:9876" \
     foxiswho/rocketmq:4.8.0 sh mqbroker
   ```

2. **配置数据库**
   ```bash
   # 执行数据库初始化脚本
   mysql -h localhost -u root -p123456 chat_db < docs/sql/init.sql
   ```

3. **启动聊天服务**
   ```bash
   cd chat-service
   mvn clean compile
   mvn spring-boot:run
   ```

4. **访问系统**
   - **聊天界面**: http://localhost:8083
   - **API文档**: http://localhost:8083/doc.html
   - **Nacos控制台**: http://localhost:8848/nacos (nacos/nacos)
   - **WebSocket地址**: ws://localhost:9090/ws

## 📱 使用指南

### 🔗 建立连接
1. **访问聊天界面**: 打开浏览器访问 http://localhost:8083
2. **输入用户信息**:
   - 用户ID：1001（或任意数字ID）
   - Token：user_1001（格式：user_用户ID）
3. **建立连接**: 点击"连接WebSocket"按钮
4. **连接确认**: 看到"WebSocket连接成功"提示

### 💬 私聊功能
1. **指定接收者**: 在"接收者ID"输入框输入目标用户ID（如：1002）
2. **输入消息**: 在消息输入框输入要发送的内容
3. **发送消息**: 点击"发送消息"按钮
4. **查看结果**: 消息会实时显示在聊天区域，格式为`[私聊] 发送者ID: 消息内容`

### 🏠 群聊功能
1. **查看房间**: 点击"加载公开房间"按钮查看可用聊天室
2. **加入房间**: 选择房间并点击"加入房间"按钮
3. **发送群聊**: 清空"接收者ID"输入框，输入消息内容
4. **群聊消息**: 点击"发送消息"，消息会广播给房间内所有用户

### 📁 文件传输
1. **选择文件**: 点击"选择文件"按钮选择要发送的文件
2. **上传文件**: 点击"上传文件"按钮
3. **发送文件**: 文件上传成功后会自动发送文件消息
4. **下载文件**: 点击聊天记录中的文件链接即可下载

### 🧪 多用户测试
1. **方法一**: 使用不同浏览器（Chrome、Firefox、Edge等）
2. **方法二**: 使用同一浏览器的不同标签页或无痕模式
3. **用户设置**: 为每个连接设置不同的用户ID（如：1001、1002、1003）
4. **测试场景**:
   - 私聊：用户1001向1002发送消息
   - 群聊：多个用户加入同一房间进行群聊
   - 离线消息：一个用户离线时，其他用户发送消息，上线后查看离线消息

## ⚙️ 配置说明

### 核心配置文件
项目使用Nacos作为配置中心，主要配置文件：
- `configs/chat-service-dev.yml` - 开发环境配置
- `application.yml` - 应用基础配置
- `bootstrap.yml` - 启动配置

### 数据库配置
```yaml
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *******************************************************************************************************
    username: root
    password: 123456
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 20
```

### Redis 配置
```yaml
spring:
  data:
    redis:
      host: localhost
      port: 6379
      database: 0
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
```

### RocketMQ 配置
```yaml
rocketmq:
  name-server: localhost:9876
  producer:
    group: chat-producer-group
    send-message-timeout: 3000
    retry-times-when-send-failed: 2
  consumer:
    group: chat-consumer-group
```

### WebSocket 配置
```yaml
chat:
  websocket:
    port: 9090
    path: /ws
    max-frame-size: 65536
    heartbeat-interval: 30000
    connection-timeout: 60000
```

### Nacos 配置
```yaml
spring:
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
        namespace: public
        group: DEFAULT_GROUP
      config:
        server-addr: localhost:8848
        file-extension: yml
        group: DEFAULT_GROUP
```

## 🐛 故障排除

### 常见问题及解决方案

#### 1. 服务启动问题
**问题**: 服务启动失败或端口冲突
```bash
# 检查端口占用
netstat -tulpn | grep :8083
netstat -tulpn | grep :9090

# 杀死占用进程
kill -9 <PID>

# 修改配置文件中的端口
server.port=8084
chat.websocket.port=9091
```

#### 2. 数据库连接问题
**问题**: 数据库连接失败
```bash
# 检查MySQL服务状态
systemctl status mysql
# 或
docker ps | grep mysql

# 测试数据库连接
mysql -h localhost -u root -p123456 -e "SELECT 1"

# 创建数据库（如果不存在）
mysql -u root -p123456 -e "CREATE DATABASE IF NOT EXISTS chat_db"
```

#### 3. WebSocket连接问题
**问题**: WebSocket连接失败或频繁断开
- **检查防火墙**: 确保9090端口开放
- **浏览器兼容性**: 使用现代浏览器（Chrome 80+, Firefox 75+）
- **网络代理**: 检查是否有代理服务器阻止WebSocket连接
- **连接数限制**: 检查系统文件描述符限制

#### 4. 消息发送失败
**问题**: 消息无法发送或接收
- **用户认证**: 确保Token格式正确（user_用户ID）
- **用户状态**: 检查目标用户是否在线
- **房间权限**: 确认用户已加入目标房间
- **消息格式**: 检查消息JSON格式是否正确

#### 5. RocketMQ相关问题
**问题**: 消息队列异常
```bash
# 检查RocketMQ服务状态
docker ps | grep rocketmq

# 查看RocketMQ日志
docker logs rmqnamesrv
docker logs rmqbroker

# 重启RocketMQ服务
docker restart rmqnamesrv rmqbroker
```

### 日志查看
```bash
# 查看应用日志
tail -f chat-service/logs/chat-service.log

# 查看错误日志
grep ERROR chat-service/logs/chat-service.log

# 查看WebSocket连接日志
grep WebSocket chat-service/logs/chat-service.log
```

## 📊 性能指标

### 系统性能
- **并发连接数**: 支持 10,000+ WebSocket 并发连接
- **消息吞吐量**: 单节点 10,000+ 消息/秒
- **响应时间**: 平均 < 50ms（局域网环境）
- **内存占用**: 基础运行 < 512MB
- **CPU使用率**: 正常负载 < 20%

### 扩展性能
- **水平扩展**: 支持多节点部署，线性扩展
- **存储扩展**: 支持数据库读写分离和分库分表
- **缓存性能**: Redis集群支持，毫秒级响应
- **可用性**: 99.9%+ 服务可用性保证

## 📚 文档导航

### 📖 核心文档
- **[项目分析总结](PROJECT_ANALYSIS_SUMMARY.md)** - 全面的项目技术分析和评估报告
- **[项目分析文档](PROJECT_ANALYSIS.md)** - 详细的技术架构和代码分析
- **[项目总结文档](PROJECT_SUMMARY.md)** - 项目概览和技术亮点总结
- **[API接口文档](API_DOCUMENTATION.md)** - 完整的REST API和WebSocket协议文档
- **[数据库设计文档](DATABASE_DESIGN.md)** - 数据库表结构和设计说明

### 🚀 部署运维
- **[部署运维指南](DEPLOYMENT_GUIDE.md)** - 生产环境部署和运维指南
- **[OAuth2配置指南](OAUTH2_SETUP_GUIDE.md)** - OAuth2认证服务配置说明
- **[OAuth2使用文档](OAUTH2_USAGE.md)** - OAuth2认证使用指南

### 🧪 测试文档
- **[测试指南](TESTING_GUIDE.md)** - 功能测试和性能测试指南
- **[聊天测试页面说明](CHAT_TEST_PAGE_README.md)** - 前端测试页面使用说明
- **[引用回复功能测试指南](MESSAGE_REPLY_TESTING_GUIDE.md)** - 消息引用回复功能测试说明

### 📊 项目报告
- **[项目状态报告](PROJECT_STATUS_REPORT.md)** - 项目当前状态和进展报告
- **[最新功能报告](LATEST_FEATURES_REPORT.md)** - 最新功能特性报告
- **[引用回复功能验证报告](REPLY_FUNCTIONALITY_VALIDATION_REPORT.md)** - 引用回复功能验证结果
- **[引用回复功能使用说明](REPLY_FUNCTIONALITY_USAGE.md)** - 引用回复功能使用指南
- **[引用回复功能最终报告](REPLY_FUNCTIONALITY_FINAL_REPORT.md)** - 引用回复功能完整验证报告

## 🔄 版本历史

### v1.2.2 (2025-01-27) - 当前版本 🚀
- ✅ **文档体系完善**: 全面更新项目文档，完善技术架构说明
- ✅ **OAuth2认证集成**: 完整的OAuth2认证服务和JWT令牌管理
- ✅ **微服务架构优化**: 完善网关服务、认证服务、聊天服务的模块化设计
- ✅ **监控体系增强**: 集成Prometheus监控和Micrometer指标收集
- ✅ **容器化部署**: 完善Docker Compose部署方案
- ✅ **安全性提升**: 增强文件上传安全检测和权限控制
- ✅ **性能优化**: 优化数据库连接池和缓存策略

### v1.2.1 (2025-01-26)
- ✅ **关键问题修复**: 彻底解决在线用户数量与用户列表不一致问题
- ✅ **消息类型统一**: 修复前后端消息类型不匹配，统一使用'user_list'类型
- ✅ **房间管理优化**: 修复用户认证后自动加入默认房间逻辑
- ✅ **异步广播机制**: 实现用户状态变更的异步广播，提升性能
- ✅ **依赖配置完善**: 添加RestTemplate配置，解决服务启动问题
- ✅ **用户体验提升**: 实现实时、准确的在线用户状态显示
- ✅ **系统稳定性**: 增强错误处理和异常恢复机制
- ✅ **性能优化**: 专用线程池处理广播任务，避免主线程阻塞

### v1.2.0 (2025-01-24)
- ✅ **生产就绪**: 系统已达到生产环境部署标准，所有核心功能稳定运行
- ✅ **架构优化**: 完善分布式架构设计，支持万级并发用户
- ✅ **功能增强**: 新增文件上传下载、图片消息、离线通知等完整功能
- ✅ **引用回复**: 完整实现消息引用回复功能，支持链式回复和权限验证
- ✅ **性能优化**: 优化WebSocket连接管理和消息路由，毫秒级响应
- ✅ **监控完善**: 集成Prometheus监控、健康检查、自动故障转移
- ✅ **文档完善**: 更新所有技术文档，提供完整的部署和运维指南

### v1.1.0 (2025-01-20)
- ✅ **分布式支持**: 实现多节点部署和跨节点消息路由
- ✅ **离线消息**: 完善离线消息存储和推送机制
- ✅ **房间管理**: 增强房间权限管理和成员控制
- ✅ **异常处理**: 完善全局异常处理和错误恢复

### v1.0.0 (2025-01-15)
- ✅ **基础功能**: 完成核心聊天功能开发
- ✅ **实时通信**: 实现WebSocket实时消息推送
- ✅ **数据持久化**: 集成MySQL和Redis存储
- ✅ **消息队列**: 集成RocketMQ分布式消息处理

## � 版本更新

### v1.2.3 (2025-01-28) - 当前版本
- ✅ **项目分析**: 完成全面的项目技术分析和文档更新
- ✅ **代码质量**: 优化异常处理机制和设计模式应用
- ✅ **性能优化**: 改进缓存策略和连接管理
- ✅ **安全加固**: 完善OAuth2认证和权限控制
- ✅ **部署优化**: 改进Docker部署配置和监控体系
- ✅ **文档完善**: 新增项目分析总结文档

### v1.2.2 (2025-01-27)
- ✅ **引用回复**: 新增消息引用回复功能
- ✅ **OAuth2认证**: 完善OAuth2认证流程
- ✅ **文件上传**: 优化文件上传接口
- ✅ **错误处理**: 增强错误处理机制

## �🚀 未来规划

### v1.3.0 (计划中)
- 🔄 **音视频通话**: 集成WebRTC实现音视频通话功能
- 🔄 **消息加密**: 实现端到端消息加密
- 🔄 **AI集成**: 智能聊天机器人和内容审核
- 🔄 **移动端支持**: 开发移动端APP

### v1.4.0 (规划中)
- 🔄 **微服务拆分**: 拆分用户服务、文件服务等独立微服务
- 🔄 **容器化部署**: 完善Docker和Kubernetes部署方案
- 🔄 **性能优化**: 数据库分库分表，缓存优化
- 🔄 **国际化**: 多语言支持

## 🤝 贡献指南

我们欢迎所有形式的贡献！

### 如何贡献
1. **Fork** 本仓库
2. **创建** 特性分支 (`git checkout -b feature/AmazingFeature`)
3. **提交** 更改 (`git commit -m 'Add some AmazingFeature'`)
4. **推送** 到分支 (`git push origin feature/AmazingFeature`)
5. **创建** Pull Request

### 贡献类型
- 🐛 **Bug修复**: 发现并修复系统bug
- ✨ **新功能**: 开发新的功能特性
- 📚 **文档**: 改进文档和注释
- 🎨 **UI/UX**: 改进用户界面和体验
- ⚡ **性能**: 性能优化和改进
- 🧪 **测试**: 添加或改进测试用例

## 📄 许可证

本项目采用 [MIT License](LICENSE) 开源协议。

## 🙏 致谢

感谢以下开源项目和技术社区：
- [Spring Boot](https://spring.io/projects/spring-boot)
- [Spring Cloud Alibaba](https://github.com/alibaba/spring-cloud-alibaba)
- [Netty](https://netty.io/)
- [RocketMQ](https://rocketmq.apache.org/)
- [Nacos](https://nacos.io/)

---

**⭐ 如果这个项目对你有帮助，请给个Star支持一下！**
