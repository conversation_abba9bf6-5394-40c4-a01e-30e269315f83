# SpringCloud 聊天微服务部署指南

## 🎯 项目状态更新 (2025-01-27)

### ✅ 系统功能状态
经过全面测试验证，SpringCloud Alibaba聊天微服务系统所有核心功能已稳定运行：

#### 🚀 核心功能
- **✅ 多用户实时聊天** - 支持万级并发用户同时在线通信
- **✅ 私聊功能** - 用户间一对一聊天，支持离线消息推送
- **✅ 群聊功能** - 房间内多人实时聊天，支持权限管理
- **✅ 文件传输** - 支持文件上传下载，多种文件类型
- **✅ 图片消息** - 支持图片发送，自动生成缩略图
- **✅ OAuth2认证** - 完整的OAuth2认证和JWT令牌管理
- **✅ 引用回复** - 支持消息引用回复和链式对话

#### 🔧 技术特性
- **✅ WebSocket 连接管理** - 稳定的连接建立和维护
- **✅ 分布式消息传递** - 基于 RocketMQ 的跨节点消息路由
- **✅ 心跳机制** - 连接健康检查和自动重连
- **✅ 消息持久化** - 聊天记录存储和查询
- **✅ 离线消息** - 离线用户消息存储和推送
- **✅ 节点管理** - 分布式节点发现和故障转移
- **✅ 监控告警** - Prometheus 监控指标和健康检查
- **✅ 微服务架构** - 完整的微服务拆分和服务治理

### 🔧 已解决的关键技术问题
1. **Netty @Sharable 处理器问题** - 修复多用户连接冲突，支持万级并发
2. **MyBatis-Plus 自动填充** - 解决数据库字段自动填充，完善审计功能
3. **RocketMQ 消息类型转换** - 修复消息消费类型问题，确保消息可靠传递
4. **心跳超时机制** - 优化节点清理策略，避免误删活跃节点
5. **WebSocket 消息验证** - 完善消息类型处理和验证机制
6. **客户端消息处理** - 修复前端消息解析和DOM操作问题
7. **分布式锁优化** - 解决分布式环境下的并发问题
8. **文件上传安全** - 完善文件类型验证和安全检查

## 📋 概述

本文档提供了 SpringCloud Alibaba 聊天微服务系统的完整部署指南，涵盖开发环境、测试环境和生产环境的部署方案。支持单机部署、Docker 容器化部署和 Kubernetes 集群部署等多种方式。

### 系统架构
- **chat-service**: 聊天核心服务，提供WebSocket实时通信
- **auth-service**: 认证授权服务，提供OAuth2认证
- **gateway**: API网关服务，提供路由转发和负载均衡
- **springcloud-alibaba-mybaits**: 数据访问层公共模块

## 🛠️ 环境要求

### 基础环境要求
- **操作系统**: Linux (推荐 Ubuntu 20.04+/CentOS 8+) / Windows 10+ / macOS 10.15+
- **JDK**: 17 或更高版本 (推荐 OpenJDK 17)
- **Maven**: 3.6.3 或更高版本
- **Git**: 2.20+ (用于代码拉取)
- **Docker**: 20.10+ (容器化部署)
- **Docker Compose**: 2.0+ (编排部署)

### 硬件资源要求

#### 开发环境 (最小配置)
- **CPU**: 2 核心
- **内存**: 4GB RAM
- **存储**: 20GB 可用空间
- **网络**: 100Mbps

#### 生产环境 (推荐配置)
- **CPU**: 4 核心以上
- **内存**: 8GB RAM 以上
- **存储**: 100GB SSD
- **网络**: 1Gbps 以上

### 依赖服务版本
- **MySQL**: 8.0+ (推荐 8.0.33)
- **Redis**: 6.0+ (推荐 7.0.11)
- **RocketMQ**: 4.9+ (推荐 5.1.4)
- **Nacos**: 2.0+ (推荐 2.3.2)

### 端口规划
- **8083**: HTTP 服务端口
- **9090**: WebSocket 服务端口
- **3306**: MySQL 数据库端口
- **6379**: Redis 缓存端口
- **8848**: Nacos 服务端口
- **9876**: RocketMQ NameServer 端口
- **10909/10911**: RocketMQ Broker 端口

## 🚀 快速开始

### 1. 使用 Docker Compose 部署（推荐）

#### 最小化部署
适用于开发和测试环境，只启动必要的依赖服务。

```bash
# 进入项目目录
cd chat-service/docs

# 启动最小化服务（Redis + RocketMQ）
docker-compose -f docker-compose-minimal.yml up -d

# 查看服务状态
docker-compose -f docker-compose-minimal.yml ps

# 查看日志
docker-compose -f docker-compose-minimal.yml logs -f
```

#### 完整部署
包含所有依赖服务和监控组件。

```bash
# 启动完整服务
docker-compose up -d

# 查看服务状态
docker-compose ps
```

### 2. 启动应用服务

```bash
# 返回项目根目录
cd ../..

# 编译项目
mvn clean compile

# 启动 chat-service
cd chat-service
mvn spring-boot:run
```

### 3. 验证部署

- **应用健康检查**: http://localhost:8083/actuator/health
- **API 文档**: http://localhost:8083/doc.html
- **WebSocket 测试**: ws://localhost:9090/ws
- **RocketMQ 控制台**: http://localhost:8080

## 🐳 Docker 部署

### 1. 构建应用镜像

```bash
# 在项目根目录执行
mvn clean package -DskipTests

# 构建 chat-service 镜像
cd chat-service
docker build -t chat-service:latest .
```

### 2. 运行应用容器

```bash
# 运行 chat-service 容器
docker run -d \
  --name chat-service \
  --network host \
  -p 8083:8083 \
  -p 9090:9090 \
  -e SPRING_PROFILES_ACTIVE=prod \
  -e NACOS_SERVER_ADDR=*************:8848 \
  -e MYSQL_HOST=************* \
  -e REDIS_HOST=************* \
  -e ROCKETMQ_NAMESERVER=*************:9876 \
  chat-service:latest
```

### 3. Docker Compose 完整配置

创建 `docker-compose-prod.yml`:

```yaml
version: '3.8'

services:
  chat-service:
    image: chat-service:latest
    container_name: chat-service
    ports:
      - "8083:8083"
      - "9090:9090"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - NACOS_SERVER_ADDR=nacos:8848
      - MYSQL_HOST=mysql
      - REDIS_HOST=redis
      - ROCKETMQ_NAMESERVER=rocketmq-nameserver:9876
    depends_on:
      - mysql
      - redis
      - rocketmq-nameserver
      - rocketmq-broker
      - nacos
    networks:
      - chat-network

  mysql:
    image: mysql:8.0
    container_name: chat-mysql
    environment:
      MYSQL_ROOT_PASSWORD: root123
      MYSQL_DATABASE: chat_db
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/init:/docker-entrypoint-initdb.d
    networks:
      - chat-network

  redis:
    image: redis:7-alpine
    container_name: chat-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - chat-network

  rocketmq-nameserver:
    image: apache/rocketmq:4.9.4
    container_name: rocketmq-nameserver
    ports:
      - "9876:9876"
    environment:
      - JAVA_OPT_EXT=-server -Xms512m -Xmx512m
    command: sh mqnamesrv
    networks:
      - chat-network

  rocketmq-broker:
    image: apache/rocketmq:4.9.4
    container_name: rocketmq-broker
    ports:
      - "10909:10909"
      - "10911:10911"
      - "10912:10912"
    environment:
      - NAMESRV_ADDR=rocketmq-nameserver:9876
      - JAVA_OPT_EXT=-server -Xms512m -Xmx512m
    command: sh mqbroker -c /opt/rocketmq-4.9.4/conf/broker.conf
    depends_on:
      - rocketmq-nameserver
    networks:
      - chat-network

  nacos:
    image: nacos/nacos-server:v2.2.3
    container_name: chat-nacos
    ports:
      - "8848:8848"
      - "9848:9848"
    environment:
      - MODE=standalone
      - SPRING_DATASOURCE_PLATFORM=mysql
      - MYSQL_SERVICE_HOST=mysql
      - MYSQL_SERVICE_DB_NAME=nacos_config
      - MYSQL_SERVICE_USER=root
      - MYSQL_SERVICE_PASSWORD=root123
    depends_on:
      - mysql
    networks:
      - chat-network

volumes:
  mysql_data:
  redis_data:

networks:
  chat-network:
    driver: bridge
```

## ⚙️ 配置管理

### 1. Nacos 配置

在 Nacos 控制台中创建配置文件：

**Data ID**: `chat-service-prod.yml`
**Group**: `DEFAULT_GROUP`

```yaml
# 生产环境配置
server:
  port: 8083

spring:
  datasource:
    url: jdbc:mysql://${MYSQL_HOST:localhost}:3306/chat_db?useUnicode=true&characterEncoding=utf8&serverTimezone=Asia/Shanghai
    username: ${MYSQL_USERNAME:root}
    password: ${MYSQL_PASSWORD:root123}
    driver-class-name: com.mysql.cj.jdbc.Driver
    
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}
    database: 0
    
rocketmq:
  name-server: ${ROCKETMQ_NAMESERVER:localhost:9876}
  producer:
    group: chat-producer-group
    send-message-timeout: 3000
    retry-times-when-send-failed: 2

# Netty 配置
netty:
  websocket:
    port: 9090
    path: /ws
    max-frame-size: 65536
    max-connections: 10000

# 聊天服务配置
chat:
  message:
    max-length: 1000
    history-size: 100
    offline-expire: 7d
  room:
    max-users: 500
    max-rooms: 1000
  file:
    max-size: 10MB
    upload-path: /data/chat/files
```

### 2. 环境变量配置

创建 `.env` 文件：

```bash
# 数据库配置
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USERNAME=root
MYSQL_PASSWORD=root123
MYSQL_DATABASE=chat_db

# Redis 配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# RocketMQ 配置
ROCKETMQ_NAMESERVER=localhost:9876

# Nacos 配置
NACOS_SERVER_ADDR=localhost:8848
NACOS_USERNAME=nacos
NACOS_PASSWORD=nacos

# 应用配置
SPRING_PROFILES_ACTIVE=prod
SERVER_PORT=8083
WEBSOCKET_PORT=9090
```

## 🔧 生产环境部署

### 1. 服务器配置建议

#### 最小配置
- **CPU**: 2 核
- **内存**: 4GB
- **磁盘**: 50GB SSD
- **网络**: 100Mbps

#### 推荐配置
- **CPU**: 4 核
- **内存**: 8GB
- **磁盘**: 100GB SSD
- **网络**: 1Gbps

### 2. JVM 参数优化

```bash
# 启动参数
java -server \
  -Xms2g -Xmx2g \
  -XX:+UseG1GC \
  -XX:MaxGCPauseMillis=200 \
  -XX:+HeapDumpOnOutOfMemoryError \
  -XX:HeapDumpPath=/data/logs/chat-service/ \
  -Dspring.profiles.active=prod \
  -jar chat-service.jar
```

### 3. Nginx 反向代理配置

```nginx
upstream chat-service {
    server 127.0.0.1:8083;
    # 如果有多个实例
    # server 127.0.0.1:8084;
}

upstream chat-websocket {
    server 127.0.0.1:9090;
    # server 127.0.0.1:9091;
}

server {
    listen 80;
    server_name chat.example.com;
    
    # HTTP API 代理
    location /api/ {
        proxy_pass http://chat-service;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # WebSocket 代理
    location /ws {
        proxy_pass http://chat-websocket;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_read_timeout 86400;
    }
}
```

### 4. 系统服务配置

创建 systemd 服务文件 `/etc/systemd/system/chat-service.service`:

```ini
[Unit]
Description=Chat Service
After=network.target

[Service]
Type=simple
User=chatuser
Group=chatuser
WorkingDirectory=/opt/chat-service
ExecStart=/usr/bin/java -server -Xms2g -Xmx2g -XX:+UseG1GC -Dspring.profiles.active=prod -jar chat-service.jar
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
```

启动服务：
```bash
sudo systemctl daemon-reload
sudo systemctl enable chat-service
sudo systemctl start chat-service
sudo systemctl status chat-service
```

## 📊 监控和日志

### 1. 应用监控

#### Prometheus 配置
```yaml
# prometheus.yml
scrape_configs:
  - job_name: 'chat-service'
    static_configs:
      - targets: ['localhost:8083']
    metrics_path: '/actuator/prometheus'
    scrape_interval: 15s
```

#### Grafana 仪表板
导入预配置的仪表板模板，监控以下指标：
- JVM 内存使用情况
- WebSocket 连接数
- 消息发送成功率
- 数据库连接池状态
- Redis 缓存命中率

### 2. 日志管理

#### 日志配置 (logback-spring.xml)
```xml
<configuration>
    <springProfile name="prod">
        <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
            <file>/data/logs/chat-service/chat-service.log</file>
            <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
                <fileNamePattern>/data/logs/chat-service/chat-service.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
                <maxFileSize>100MB</maxFileSize>
                <maxHistory>30</maxHistory>
                <totalSizeCap>10GB</totalSizeCap>
            </rollingPolicy>
            <encoder>
                <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
            </encoder>
        </appender>
        
        <root level="INFO">
            <appender-ref ref="FILE"/>
        </root>
    </springProfile>
</configuration>
```

## 🔒 安全配置

### 1. 防火墙设置
```bash
# 开放必要端口
sudo ufw allow 8083/tcp  # HTTP API
sudo ufw allow 9090/tcp  # WebSocket
sudo ufw enable
```

### 2. SSL/TLS 配置
```yaml
# application-prod.yml
server:
  ssl:
    enabled: true
    key-store: classpath:keystore.p12
    key-store-password: changeit
    key-store-type: PKCS12
    key-alias: chat-service
```

## 🚨 故障排查

### 常见问题

1. **WebSocket 连接失败**
   - 检查防火墙设置
   - 确认 Nginx 代理配置
   - 查看应用日志

2. **消息发送失败**
   - 检查 RocketMQ 连接状态
   - 确认用户认证状态
   - 查看消息队列积压情况

3. **数据库连接异常**
   - 检查数据库服务状态
   - 确认连接池配置
   - 查看数据库日志

### 日志查看命令
```bash
# 查看应用日志
tail -f /data/logs/chat-service/chat-service.log

# 查看系统服务日志
sudo journalctl -u chat-service -f

# 查看 Docker 容器日志
docker logs -f chat-service
```
